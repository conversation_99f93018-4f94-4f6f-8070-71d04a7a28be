import User from '../../models/User';
import Api<PERSON>ey from '../../models/ApiKey';
import request from 'supertest';
import app from '../../server';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } from '../data/Auth';
import { setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import Vessel from '../../models/Vessel';
import RegionGroup from '../../models/RegionGroup';
import streamService from '../../services/Stream.service';
import { AuthRunTestsFunction } from '../type';
import { vesselsList, streamsList, regionGroupsList } from '../data/Vessels';
import { canAccessVessel } from '../mocks/utils/functions.mock';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../models/Vessel', () => require('../mocks/models/vessel.mock'));
jest.mock('../../models/RegionGroup', () => require('../mocks/models/regionGroup.mock'));
jest.mock('../../services/Stream.service', () => require('../mocks/services/stream.mock'));
jest.mock('../../utils/functions', () => require('../mocks/utils/functions.mock'));


describe('Vessels API', () => {
    describe('GET /api/vessels/info', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/vessels/info');
                    expect(res.status).toBe(401);
                });

                it('should return 200 and fetch vessels info', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (canAccessVessel as jest.Mock).mockResolvedValueOnce(true as never);
                    (Vessel as any).find.mockResolvedValueOnce(vesselsList);
                    (streamService as any).fetchAll.mockResolvedValueOnce(streamsList);
                    (RegionGroup as any).find.mockResolvedValueOnce(regionGroupsList);

                    const res = await request(app)
                        .get('/api/vessels/info?regions=ap-southeast-1&region_groups=507f1f77bcf86cd799439012')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(Array.isArray(res.body)).toBe(true);
                });

                it('should return 200 with vessels without region_group_id', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (canAccessVessel as jest.Mock).mockResolvedValueOnce(true as never);
                    (Vessel as any).find.mockResolvedValueOnce(vesselsList.map((vessel) => {
                        vessel.region_group_id = null as any;
                        return vessel;
                    }));
                    (streamService as any).fetchAll.mockResolvedValueOnce(streamsList.map((stream, index) => {
                        stream.unit_id = 'unit-' + index;
                        stream.region = null as any;
                        stream.is_live = false;
                        return stream;
                    }));
                    (RegionGroup as any).find.mockResolvedValueOnce(regionGroupsList);

                    const res = await request(app)
                        .get('/api/vessels/info?regions=ap-southeast-1&region_groups=507f1f77bcf86cd799439012')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(Array.isArray(res.body)).toBe(true);
                });

                it('should return 200 with filtered vessels by regions', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Vessel as any).find.mockResolvedValueOnce(vesselsList);
                    (streamService as any).fetchAll.mockResolvedValueOnce(streamsList);
                    (RegionGroup as any).find.mockResolvedValueOnce(regionGroupsList);

                    const res = await request(app)
                        .get('/api/vessels/info?regions=ap-southeast-1')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(Array.isArray(res.body)).toBe(true);
                });

                it('should return 200 with filtered vessels by region_groups', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Vessel as any).find.mockResolvedValueOnce(vesselsList);
                    (streamService as any).fetchAll.mockResolvedValueOnce(streamsList);
                    (RegionGroup as any).find.mockResolvedValueOnce(regionGroupsList);

                    const res = await request(app)
                        .get('/api/vessels/info?region_groups=507f1f77bcf86cd799439012')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(Array.isArray(res.body)).toBe(true);
                });

                it('should return 200 with empty region_groups filter', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Vessel as any).find.mockResolvedValueOnce(vesselsList);
                    (streamService as any).fetchAll.mockResolvedValueOnce(streamsList);
                    (RegionGroup as any).find.mockResolvedValueOnce(regionGroupsList);

                    const res = await request(app)
                        .get('/api/vessels/info?region_groups=')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(Array.isArray(res.body)).toBe(true);
                });

                it('should return 200 with invalid region_groups filter', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Vessel as any).find.mockResolvedValueOnce(vesselsList);
                    (streamService as any).fetchAll.mockResolvedValueOnce(streamsList);
                    (RegionGroup as any).find.mockResolvedValueOnce(regionGroupsList);

                    const res = await request(app)
                        .get('/api/vessels/info?region_groups=invalid-id')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(Array.isArray(res.body)).toBe(true);
                });

                it('should return 200 with multiple regions filter', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Vessel as any).find.mockResolvedValueOnce(vesselsList);
                    (streamService as any).fetchAll.mockResolvedValueOnce(streamsList);
                    (RegionGroup as any).find.mockResolvedValueOnce(regionGroupsList);

                    const res = await request(app)
                        .get('/api/vessels/info?regions=ap-southeast-1,us-east-1')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(Array.isArray(res.body)).toBe(true);
                });

                it('should return 200 with multiple region_groups filter', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Vessel as any).find.mockResolvedValueOnce(vesselsList);
                    (streamService as any).fetchAll.mockResolvedValueOnce(streamsList);
                    (RegionGroup as any).find.mockResolvedValueOnce(regionGroupsList);

                    const res = await request(app)
                        .get('/api/vessels/info?region_groups=507f1f77bcf86cd799439012,507f1f77bcf86cd799439014')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(Array.isArray(res.body)).toBe(true);
                });

                it('should return 200 with combined filters', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Vessel as any).find.mockResolvedValueOnce(vesselsList);
                    (streamService as any).fetchAll.mockResolvedValueOnce(streamsList);
                    (RegionGroup as any).find.mockResolvedValueOnce(regionGroupsList);

                    const res = await request(app)
                        .get('/api/vessels/info?regions=ap-southeast-1&region_groups=507f1f77bcf86cd799439012')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(Array.isArray(res.body)).toBe(true);
                });

                it('should handle vessels without region_group_id', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const vesselsWithoutRegionGroup = vesselsList.map(vessel => ({ ...vessel, region_group_id: null }));
                    (Vessel as any).find.mockResolvedValueOnce(vesselsWithoutRegionGroup);
                    (streamService as any).fetchAll.mockResolvedValueOnce(streamsList);
                    (RegionGroup as any).find.mockResolvedValueOnce(regionGroupsList);

                    const res = await request(app)
                        .get('/api/vessels/info?region_groups=507f1f77bcf86cd799439012')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(Array.isArray(res.body)).toBe(true);
                });

                it('should handle vessels without matching stream', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Vessel as any).find.mockResolvedValueOnce(vesselsList);
                    (streamService as any).fetchAll.mockResolvedValueOnce([]);
                    (RegionGroup as any).find.mockResolvedValueOnce(regionGroupsList);

                    const res = await request(app)
                        .get('/api/vessels/info?regions=ap-southeast-1')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(Array.isArray(res.body)).toBe(true);
                });

                it('should handle conditional rate limiter for user', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Vessel as any).find.mockResolvedValueOnce(vesselsList);
                    (streamService as any).fetchAll.mockResolvedValueOnce(streamsList);
                    (RegionGroup as any).find.mockResolvedValueOnce(regionGroupsList);

                    const res = await request(app)
                        .get('/api/vessels/info')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(Array.isArray(res.body)).toBe(true);
                });

                it('should handle conditional rate limiter for api-key', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Vessel as any).find.mockResolvedValueOnce(vesselsList);
                    (streamService as any).fetchAll.mockResolvedValueOnce(streamsList);
                    (RegionGroup as any).find.mockResolvedValueOnce(regionGroupsList);

                    const res = await request(app)
                        .get('/api/vessels/info')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(Array.isArray(res.body)).toBe(true);
                });

                it('should handle region filtering with no matching regions', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Vessel as any).find.mockResolvedValueOnce(vesselsList);
                    (streamService as any).fetchAll.mockResolvedValueOnce(streamsList);
                    (RegionGroup as any).find.mockResolvedValueOnce(regionGroupsList);

                    const res = await request(app)
                        .get('/api/vessels/info?regions=nonexistent-region')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(Array.isArray(res.body)).toBe(true);
                });

                it('should handle region group filtering with no matching groups', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Vessel as any).find.mockResolvedValueOnce(vesselsList);
                    (streamService as any).fetchAll.mockResolvedValueOnce(streamsList);
                    (RegionGroup as any).find.mockResolvedValueOnce(regionGroupsList);

                    const res = await request(app)
                        .get('/api/vessels/info?region_groups=507f1f77bcf86cd799439999')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(Array.isArray(res.body)).toBe(true);
                });


            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});