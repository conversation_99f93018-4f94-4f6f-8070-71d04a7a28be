import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('ApiEndpoint Model', () => {
    let mockMongoose: any;
    let mockDb: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create model with endpoint_id, name, and category fields', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/ApiEndpoint')];

        const ApiEndpointModule = await import('../../models/ApiEndpoint');
        const ApiEndpoint = ApiEndpointModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('ApiEndpoint', expect.any(Object), 'api_endpoints');
        expect(ApiEndpoint).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.endpoint_id).toBeDefined();
        expect(schemaArg.paths.endpoint_id.type).toBe(Number);
        expect(schemaArg.paths.endpoint_id.required).toBe(true);
        expect(schemaArg.paths.endpoint_id.unique).toBe(true);

        expect(schemaArg.paths.name).toBeDefined();
        expect(schemaArg.paths.name.type).toBe(String);
        expect(schemaArg.paths.name.required).toBe(true);

        expect(schemaArg.paths.category).toBeDefined();
        expect(schemaArg.paths.category.type).toBe(String);
        expect(schemaArg.paths.category.required).toBe(true);
    });
});