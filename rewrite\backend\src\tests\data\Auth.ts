import { jest } from '@jest/globals';
import jwt from 'jsonwebtoken'
import mongoose from 'mongoose';
import { IAuthUser } from '../../interfaces/User';
import { endpointIds } from '../../utils/endpointIds';

const generateUserToken = (user_id: string) => {
    const token = jwt.sign({ user_id }, process.env.JWT_SECRET as string, { expiresIn: '24h' });
    authorizedUser.jwt_tokens.push(token);
    nonAuthorizedUser['jwt_tokens'].push(token);
    return token;
}

const generateApiToken = (api_key_id: string) => {
    return jwt.sign({ api_key_id }, process.env.JWT_SECRET as string, { expiresIn: '24h' });
}

const authorizedUser: IAuthUser & { save: Function } = {
    _id: '66e2e452bca74bbdb726369c',
    name: 'test-admin',
    username: 'test',
    email: '<EMAIL>',
    role_id: 1,
    deletable: false,
    creation_timestamp: new Date('2024-09-12T12:53:38.665Z'),
    is_deleted: false,
    email_verification_enabled: true,
    email_verified_device_ids: [],
    jwt_tokens: [],
    allowed_vessels: [],
    created_by: new mongoose.Types.ObjectId('66e2e452bca74bbdb726369c'),
    organization_id: '66e2e452bca74bbdb726369c',
    role: {
        _id: '66cf1154ee65876e64371c9d',
        role_id: 1,
        role_name: 'Admin',
        hierarchy_number: 1,
        denied_permissions: [],
        deletable: false,
        creation_timestamp: new Date('2024-09-03T18:24:59.698Z'),
        editable: false,
    },
    permissions: [
        {
            _id: "66d6fd7de480fbb5a5e1abc6",
            permission_id: 100,
            permission_name: "MANAGE_ROLES",
            permission_description: "User can add, remove, and update roles",
            assignable: true
        },
        {
            _id: "66d70185e480fbb5a5e1abc9",
            permission_id: 200,
            permission_name: "MANAGE_USERS",
            permission_description: "User can update roles for the users",
            assignable: true
        },
        {
            _id: "66d701b0e480fbb5a5e1abcb",
            permission_id: 300,
            permission_name: "ACCESS_ALL_VESSELS",
            permission_description: "User can switch region on the dashboard",
            assignable: true
        },
        {
            _id: "66e0546871fd26bbd48668a2",
            permission_id: 400,
            permission_name: "VIEW_SESSION_LOGS",
            permission_description: "User view session logs on the dashboard",
            assignable: true
        },
        {
            _id: "66f2ab205fc1d8f82af7c720",
            permission_id: 500,
            permission_name: "MANAGE_API_KEYS",
            permission_description: "User can manage API developer keys on the dashboard",
            assignable: false
        },
        {
            _id: "6707dd36af2c1b345bd06b8f",
            permission_id: 600,
            permission_name: "VIEW_STATISTICS",
            permission_description: "User can view statistics on the dashboard",
            assignable: true
        },
        {
            _id: "679240e27524efb8a6e0cf66",
            permission_id: 700,
            permission_name: "MANAGE_NOTIFICATIONS_ALERTS",
            permission_description: "User can manage notifications alerts",
            assignable: true
        },
        {
            _id: "67d86ed1926673c75a3da10f",
            permission_id: 800,
            permission_name: "MANAGE_REGIONS_GROUPS",
            permission_description: "User can manage regions groups",
            assignable: true
        },
        {
            _id: "67daf32c7a2d4a3308b54ad1",
            permission_id: 900,
            permission_name: "ADDITIONAL_EMAIL_ADDRESSES_PRIVILEGE",
            permission_description: "User can add any email in notification alerts",
            assignable: true
        },
        {
            _id: "67e2d9b466dfd6d9dd8222cc",
            permission_id: 1000,
            permission_name: "MANAGE_ORGANIZATIONS",
            permission_description: "User can add, remove, and update organizations",
            assignable: true
        },
        {
            _id: "67f6c3f18a24775155418da4",
            permission_id: 1100,
            permission_name: "TEST_NOTIFICATION_ALERTS",
            permission_description: "User can fetch latest notification alerts for testing",
            assignable: true
        },
        {
            _id: "682f5d2fc198f6e4dd434e1e",
            permission_id: 1200,
            permission_name: "MANAGE_VESSELS",
            permission_description: "User can add and update vessels",
            assignable: true
        },
        {
            _id: "686d5ce51b5fd43f6ad300e9",
            permission_id: 1300,
            permission_name: "MANAGE_ARTIFACTS",
            permission_description: "User can hide/unhide artifacts",
            assignable: true
        }
    ],
    organization: {
        _id: '66e2e452bca74bbdb726369c',
        name: 'Test Organization',
        domain: 'test.com',
        is_internal: true,
        is_miscellaneous: false,
        created_by: new mongoose.Types.ObjectId('66e2e452bca74bbdb726369c'),
        creation_timestamp: new Date('2024-09-03T18:24:59.698Z')
    },
    save: jest.fn().mockResolvedValue({} as never)
};

const nonAuthorizedUser: IAuthUser & { save: Function } = {
    _id: "66f4288532250d3bfa2e64bc",
    name: "test-user",
    username: "testuser",
    role_id: 2,
    deletable: true,
    is_deleted: false,
    email_verification_enabled: true,
    email_verified_device_ids: [],
    jwt_tokens: [],
    creation_timestamp: new Date("2024-09-25T15:13:09.826Z"),
    allowed_vessels: [],
    created_by: new mongoose.Types.ObjectId("66e2e452bca74bbdb726369c"),
    organization_id: "66e2e452bca74bbdb726369c",
    role: {
        _id: "66cf1178ee65876e64371c9e",
        role_id: 2,
        role_name: "User",
        hierarchy_number: 1,
        denied_permissions: [
            100,
            200,
            300,
            400,
            500,
            600
        ],
        deletable: false,
        creation_timestamp: new Date("2024-09-03T18:24:59.698Z"),
        editable: false
    },
    permissions: [],
    organization: {
        _id: "66e2e452bca74bbdb726369c",
        name: "Test Organization",
        domain: "test.com",
        is_internal: true,
        is_miscellaneous: false,
        created_by: new mongoose.Types.ObjectId("66e2e452bca74bbdb726369c"),
        creation_timestamp: new Date("2024-09-03T18:24:59.698Z")
    },
    save: jest.fn().mockResolvedValue({} as never)
}

const authorizedApiKey = {
    "_id": "66f2c43345fbceb6fc036b34",
    "description": "This is a test key",
    "allowed_endpoints": Object.values(endpointIds),
    "is_deleted": false,
    "is_revoked": false,
    "api_key": "d21e1a57f2de39b3f4fbd42cf871d9bc",
    "__v": 18,
    "requests": 140,
    "jwt_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcGlfa2V5X2lkIjoiNjZmMmM0MzM0NWZiY2ViNmZjMDM2YjM0IiwiaWF0IjoxNzI3Mzc3MDQ1LCJleHAiOjE3Mjc0NjM0NDV9.4NwPQ7O6kyFSrp0fiC__wtkDUyqO5wnd38l2VCsR0eE",
    "creation_timestamp": {
        "$date": "2024-09-24T13:52:51.178Z"
    },
    "save": jest.fn().mockResolvedValue({} as never),
    "markModified": jest.fn(),
    "requests_endpoints": {},
    "toObject": () => ({
        _id: "66f2c43345fbceb6fc036b34",
        description: "This is a test key",
        allowed_endpoints: Object.values(endpointIds),
        is_deleted: false,
        is_revoked: false,
        api_key: "d21e1a57f2de39b3f4fbd42cf871d9bc",
        __v: 18,
        requests: 140,
        jwt_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcGlfa2V5X2lkIjoiNjZmMmM0MzM0NWZiY2ViNmZjMDM2YjM0IiwiaWF0IjoxNzI3Mzc3MDQ1LCJleHAiOjE3Mjc0NjM0NDV9.4NwPQ7O6kyFSrp0fiC__wtkDUyqO5wnd38l2VCsR0eE",
        creation_timestamp: {
            "$date": "2024-09-24T13:52:51.178Z"
        },
        save: jest.fn().mockResolvedValue({} as never),
        markModified: jest.fn(),
        requests_endpoints: {},
        toObject: jest.fn().mockReturnValue({})
    }) as any
}

const nonAuthorizedApiKey = {
    "_id": "66f2c6e945fbceb6fc036b90",
    "description": "Test key 2",
    "allowed_endpoints": [],
    "is_deleted": false,
    "is_revoked": false,
    "api_key": "079fc9e755a8245654c1c768787ee24c",
    "__v": 15,
    "requests": 0,
    "jwt_token": null,
    "creation_timestamp": {
        "$date": "2024-09-24T14:04:25.153Z"
    },
    "save": jest.fn().mockResolvedValue({} as never),
    "markModified": jest.fn(),
    "toObject": () => ({
        _id: "66f2c6e945fbceb6fc036b90",
        description: "Test key 2",
        allowed_endpoints: [],
        is_deleted: false,
        is_revoked: false,
        api_key: "079fc9e755a8245654c1c768787ee24c",
        __v: 15,
        requests: 0,
        jwt_token: null,
        creation_timestamp: {
            "$date": "2024-09-24T14:04:25.153Z"
        },
        save: jest.fn().mockResolvedValue({} as never),
        markModified: jest.fn(),
        requests_endpoints: {},
        toObject: jest.fn().mockReturnValue({})
    }) as any
}

export {
    generateUserToken,
    generateApiToken,
    authorizedUser,
    nonAuthorizedUser,
    authorizedApiKey,
    nonAuthorizedApiKey,
}