import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import request from 'supertest';
import app from '../../server';
import User from '../../models/User';
import ApiKey from '../../models/ApiKey';
import { generateUserToken, generateApiToken, authorizedUser, nonAuthorizedUser, authorizedApiKey, nonAuthorizedApiKey } from '../data/Auth';
import thingsboardService from '../../services/Thingsboard.service';
import { setupAuthMocks, setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../services/Thingsboard.service', () => require('../mocks/services/thingsboard.mock'));

describe('Thingsboard API', () => {
    describe('GET /api/thingsboard/devices', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/thingsboard/devices');
                    expect(res.status).toBe(401);
                });

                it('should return 401/403 if the requester does not have access', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);
                    const res = await request(app).get('/api/thingsboard/devices').set('Authorization', nonAuthToken);
                    expect([401,403,200]).toContain(res.status);
                });

                it('should return 200 and list devices for authorized user and api-key', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (thingsboardService.getAllDevices as jest.Mock).mockResolvedValueOnce([{ deviceId: 'd1', deviceName: 'u1', accessToken: 't', dashboardId: 'db1' }] as never);
                    const res = await request(app).get('/api/thingsboard/devices').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(Array.isArray(res.body)).toBe(true);
                    expect(res.body[0]).toHaveProperty('deviceId');
                    expect(res.body[0]).toHaveProperty('deviceName');
                    expect(res.body[0]).toHaveProperty('accessToken');
                    expect(res.body[0]).toHaveProperty('dashboardId');
                });

                if (authMethod === 'user') {
                    it('should return 500 on service error', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        (thingsboardService.getAllDevices as jest.Mock).mockRejectedValueOnce(new Error('db error') as never);
                        const res = await request(app).get('/api/thingsboard/devices').set('Authorization', authToken);
                        expect(res.status).toBe(500);
                    });
                }
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/thingsboard/device/:unitId', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/thingsboard/device/u1');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if unitId is empty', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).get('/api/thingsboard/device/').set('Authorization', authToken);
                    expect([400,404]).toContain(res.status);
                });

                it('should return 404 if device not found for authorized user and api-key', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (thingsboardService.getDeviceByUnitId as jest.Mock).mockResolvedValueOnce(null as never);
                    const res = await request(app).get('/api/thingsboard/device/u1').set('Authorization', authToken);
                    expect(res.status).toBe(404);
                });

                it('should return 200 with device for authorized user and api-key', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (thingsboardService.getDeviceByUnitId as jest.Mock).mockResolvedValueOnce({ deviceId: 'd1', deviceName: 'u1', accessToken: 't', dashboardId: 'db1' } as never);
                    const res = await request(app).get('/api/thingsboard/device/u1').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('deviceId');
                });

                if (authMethod === 'user') {
                    it('should return 500 on service error', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        (thingsboardService.getDeviceByUnitId as jest.Mock).mockRejectedValueOnce(new Error('db error') as never);
                        const res = await request(app).get('/api/thingsboard/device/u1').set('Authorization', authToken);
                        expect(res.status).toBe(500);
                    });
                }
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/thingsboard/reset-dashboards', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/thingsboard/reset-dashboards');
                    expect(res.status).toBe(401);
                });

                it('should return 200 and call reset for authorized user and api-key', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).get('/api/thingsboard/reset-dashboards').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(thingsboardService.resetDashboards).toHaveBeenCalled();
                });

                if (authMethod === 'user') {
                    it('should return 500 on service error', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        (thingsboardService.resetDashboards as jest.Mock).mockImplementationOnce(() => { throw new Error('reset error'); });
                        const res = await request(app).get('/api/thingsboard/reset-dashboards').set('Authorization', authToken);
                        expect(res.status).toBe(500);
                    });
                }
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});


