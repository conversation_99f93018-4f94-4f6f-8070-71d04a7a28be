import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('Vessel Model', () => {
    let mockMongoose: any;
    let mockDb: any;
    let mockIoEmitter: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qmShared');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        mockIoEmitter = testSetup.mockIoEmitter;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create Vessel model with proper schema and hooks', async () => {
        jest.doMock('../../modules/ioEmitter', () => mockIoEmitter);
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);
        jest.doMock('../../models/User', () => ({ default: 'MockUser' }));

        delete require.cache[require.resolve('../../models/Vessel')];

        const VesselModule = await import('../../models/Vessel');
        const Vessel = VesselModule.default;

        expect(mockDb.qmShared.model).toHaveBeenCalledWith('Vessel', expect.any(Object), 'vessels');
        expect(Vessel).toBeDefined();

        const schemaArg = mockDb.qmShared.model.mock.calls[0][1];

        expect(schemaArg.paths.name).toBeDefined();
        expect(schemaArg.paths.name.type).toBe(String);
        expect(schemaArg.paths.name.required).toBe(true);

        expect(schemaArg.paths.thumbnail_s3_key).toBeDefined();
        expect(schemaArg.paths.thumbnail_s3_key.type).toBe(String);
        expect(schemaArg.paths.thumbnail_s3_key.required).toBe(false);
        expect(schemaArg.paths.thumbnail_s3_key.default).toBe(null);

        expect(schemaArg.paths.thumbnail_compressed_s3_key).toBeDefined();
        expect(schemaArg.paths.thumbnail_compressed_s3_key.type).toBe(String);
        expect(schemaArg.paths.thumbnail_compressed_s3_key.required).toBe(false);
        expect(schemaArg.paths.thumbnail_compressed_s3_key.default).toBe(null);

        expect(schemaArg.paths.unit_id).toBeDefined();
        expect(schemaArg.paths.unit_id.type).toBe(String);
        expect(schemaArg.paths.unit_id.required).toBe(false);
        expect(schemaArg.paths.unit_id.unique).toBe(true);
        expect(schemaArg.paths.unit_id.sparse).toBe(true);

        expect(schemaArg.paths.region_group_id).toBeDefined();
        expect(schemaArg.paths.region_group_id.type).toBeDefined();
        expect(schemaArg.paths.region_group_id.required).toBe(true);

        expect(schemaArg.paths.home_port_location).toBeDefined();
        expect(schemaArg.paths.home_port_location.type).toBe(Object);
        expect(schemaArg.paths.home_port_location.default).toBe(null);

        expect(schemaArg.paths.is_active).toBeDefined();
        expect(schemaArg.paths.is_active.type).toBe(Boolean);
        expect(schemaArg.paths.is_active.required).toBe(true);
        expect(schemaArg.paths.is_active.default).toBe(true);

        expect(schemaArg.paths.units_history).toBeDefined();
        expect(schemaArg.paths.units_history.type).toBeDefined();
        expect(schemaArg.paths.units_history.required).toBe(true);
        expect(schemaArg.paths.units_history.default).toEqual([]);

        expect(schemaArg.paths.creation_timestamp).toBeDefined();
        expect(schemaArg.paths.creation_timestamp.type).toBe(Date);
        expect(schemaArg.paths.creation_timestamp.required).toBe(true);
        expect(schemaArg.paths.creation_timestamp.default).toBeDefined();

        const timestamp = schemaArg.paths.creation_timestamp.default();
        expect(typeof timestamp).toBe('string');
        expect(timestamp).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/);

        expect(schemaArg.paths.created_by).toBeDefined();
        expect(schemaArg.paths.created_by.type).toBeDefined();
        expect(schemaArg.paths.created_by.required).toBe(true);

        expect(schemaArg.index).toHaveBeenCalledTimes(2);
        expect(schemaArg.index).toHaveBeenNthCalledWith(1, { unit_id: 1 });
        expect(schemaArg.index).toHaveBeenNthCalledWith(2, { home_port_location: "2dsphere" });

        expect(schemaArg.pre).toHaveBeenCalledWith("save", expect.any(Function));
        expect(schemaArg.post).toHaveBeenCalledTimes(3);
        expect(schemaArg.post).toHaveBeenNthCalledWith(1, "save", expect.any(Function));
        expect(schemaArg.post).toHaveBeenNthCalledWith(2, "findOneAndUpdate", expect.any(Function));
        expect(schemaArg.post).toHaveBeenNthCalledWith(3, "findOneAndDelete", expect.any(Function));

        const preSaveHookCall = schemaArg.pre.mock.calls.find((call: any) => call[0] === 'save');
        if (preSaveHookCall) {
            const preSaveHookFn = preSaveHookCall[1];
            const mockNext = jest.fn();
            
            const mockNewVessel = {
                isNew: true,
                unit_id: 'test-unit-123',
                units_history: [],
                creation_timestamp: new Date().toISOString()
            };
            preSaveHookFn.call(mockNewVessel, mockNext);
            expect(mockNext).toHaveBeenCalled();

            const mockNewVesselNoTimestamp = {
                isNew: true,
                unit_id: 'test-unit-456',
                units_history: [],
                creation_timestamp: null
            };
            preSaveHookFn.call(mockNewVesselNoTimestamp, mockNext);
            expect(mockNext).toHaveBeenCalledTimes(2);

            const mockExistingVessel = {
                isNew: false,
                isModified: jest.fn().mockReturnValue(true),
                unit_id: 'new-unit-456',
                units_history: [{ unit_id: 'old-unit', unmount_timestamp: null }]
            };
            mockExistingVessel.isModified.mockReturnValue(true);
            preSaveHookFn.call(mockExistingVessel, mockNext);
            expect(mockNext).toHaveBeenCalledTimes(3);
        }

        const mockVessel = {
            toObject: jest.fn().mockReturnValue({ _id: 'test-id', name: 'Test Vessel' })
        };

        const saveHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'save');
        if (saveHookCall) {
            const saveHookFn = saveHookCall[1];
            saveHookFn(mockVessel);
        }

        const updateHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'findOneAndUpdate');
        if (updateHookCall) {
            const updateHookFn = updateHookCall[1];
            updateHookFn(mockVessel);
        }

        const deleteHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'findOneAndDelete');
        if (deleteHookCall) {
            const deleteHookFn = deleteHookCall[1];
            deleteHookFn(mockVessel);
        }

        expect(mockIoEmitter.emit).toHaveBeenCalledTimes(3);
        expect(mockIoEmitter.emit).toHaveBeenNthCalledWith(1, "notifyAll", {
            name: "vessel/changed",
            data: { _id: 'test-id', name: 'Test Vessel' }
        });
        expect(mockIoEmitter.emit).toHaveBeenNthCalledWith(2, "notifyAll", {
            name: "vessel/changed",
            data: { _id: 'test-id', name: 'Test Vessel' }
        });
        expect(mockIoEmitter.emit).toHaveBeenNthCalledWith(3, "notifyAll", {
            name: "vessel/changed",
            data: { _id: 'test-id', name: 'Test Vessel' }
        });

        const preHookCall = schemaArg.pre.mock.calls.find((call: any) => call[0] === 'save');
        if (preHookCall) {
            const preHookFn = preHookCall[1];
            
            const mockVessel1 = {
                isModified: jest.fn().mockReturnValue(true),
                units_history: [{ unit_id: 'unit1', mount_timestamp: new Date() }],
                unit_id: 'new-unit',
                isNew: false
            };
            const next1 = jest.fn();
            preHookFn.call(mockVessel1, next1);

            const mockVessel2 = {
                isModified: jest.fn().mockReturnValue(false),
                units_history: [],
                unit_id: null,
                isNew: true
            };
            const next2 = jest.fn();
            preHookFn.call(mockVessel2, next2);

            const mockVessel3 = {
                isModified: jest.fn().mockReturnValue(true),
                units_history: [null],
                unit_id: 'new-unit',
                isNew: false
            };
            const next3 = jest.fn();
            try {
                preHookFn.call(mockVessel3, next3);
            } catch (error: any) {
                expect(error.message).toBe("Unexpected error: Last entry in units_history is undefined");
            }
        }
    });
});
