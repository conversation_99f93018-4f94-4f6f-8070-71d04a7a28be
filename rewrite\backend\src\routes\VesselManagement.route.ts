import express, { Request, Response } from "express";
import isAuthenticated from "../middlewares/auth";
import hasPermission from "../middlewares/hasPermission";
import restrictEndpointByUser from "../middlewares/restrictEndpointByUser";
import { validateData } from "../middlewares/validator";
import { body, param, query } from "express-validator";
import { validateError } from "../utils/functions";
import rateLimit from "express-rate-limit";
import assignEndpointId from "../middlewares/assignEndpointId";
import { endpointIds } from "../utils/endpointIds";
import { permissions } from "../utils/permissions";
import vesselService from "../services/Vessel.service";
import streamService from "../services/Stream.service";
import { upload, handleMulterError } from "../middlewares/multerConfig";
import { IVessel } from "../interfaces/Vessel";
import mongoose from "mongoose";

const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 10,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get(
    "/",
    assignEndpointId.bind(this, endpointIds.FETCH_PAGINATED_VESSEL_MANAGEMENT),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageVessels]),
    validateData.bind(this, [
        query("page").optional().isInt({ min: 1 }).toInt(),
        query("limit").optional().isInt({ min: 1, max: 100 }).toInt(),
        query("search").optional().isString().trim(),
    ]),
    async (req: Request, res: Response) => {
        try {
            const { page = 1, limit = 10, search = "" }: { page?: number; limit?: number; search?: string } = req.query;
            const result = await vesselService.fetchPaginated({ page, limit, search });
            res.json(result);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.get(
    "/unitIds",
    assignEndpointId.bind(this, endpointIds.FETCH_VESSEL_UNIT_IDS),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageVessels]),
    validateData.bind(this, [
        query("regions")
            .isString()
            .optional()
            .customSanitizer((v: string) => v.split(",").map((v: string) => v.trim())),
    ]),
    async (req: Request, res: Response) => {
        try {
            const { regions }: { regions?: string[] } = req.query;

            const vesselsList = await streamService.fetchAll({ regions });

            const unitDetails: { unit_id: string; name: string | null; region: string }[] = vesselsList.map((vessel) => ({
                unit_id: vessel.unit_id,
                name: vessel.name,
                region: vessel.region,
            }));

            res.json(unitDetails);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.get(
    "/assignedUnitIds",
    assignEndpointId.bind(this, endpointIds.FETCH_ASSIGNED_UNIT_IDS),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageVessels]),
    async (_req: Request, res: Response) => {
        try {
            const assignedUnitIds: string[] = await vesselService.getAllAssignedUnitIds();
            res.json(assignedUnitIds);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.get(
    "/all",
    assignEndpointId.bind(this, endpointIds.FETCH_ALL_VESSEL_MANAGEMENT),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageVessels]),
    async (_req: Request, res: Response) => {
        try {
            const vessels: IVessel[] = await vesselService.find();
            res.json(vessels);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.get(
    "/:id",
    assignEndpointId.bind(this, endpointIds.FETCH_VESSEL_MANAGEMENT_BY_ID),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageVessels]),
    validateData.bind(this, [param("id").isMongoId().withMessage("Invalid vessel ID")]),
    async (req: Request, res: Response) => {
        try {
            const { id } = req.params;
            const vessel: IVessel | null = await vesselService.findById({ id });

            if (!vessel) {
                return res.status(404).json({ message: "Vessel not found" });
            }

            res.json(vessel);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.post(
    "/",
    assignEndpointId.bind(this, endpointIds.CREATE_VESSEL_MANAGEMENT),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageVessels]),
    restrictEndpointByUser,
    upload.single("thumbnail_file"),
    handleMulterError,
    validateData.bind(this, [
        body("name").isString().trim().notEmpty().withMessage("Name is required and must be a non-empty string"),
        body("unit_id").optional().isString().withMessage("Unit ID must be a string"),
        body("is_active").optional().isBoolean().withMessage("is_active must be a boolean"),
        body("region_group_id").isMongoId().withMessage("Region group ID must be a valid Object ID"),
        body("home_port_location")
            .optional()
            .customSanitizer((value: string) => {
                if (!value) return null;
                try {
                    return JSON.parse(value);
                } catch (error) {
                    console.error("Error parsing home port location:", error);
                    throw new Error("Invalid home port coordinates format");
                }
            })
            .isArray({ min: 2, max: 2 })
            .withMessage("Home port location must be an array with exactly 2 numbers"),
        body("home_port_location.*").optional().isFloat().withMessage("Home port location must be valid numbers"),
    ]),
    async (req: Request, res: Response) => {
        try {
            const {
                name,
                unit_id,
                is_active,
                region_group_id,
                home_port_location,
            }: { name: string; unit_id?: string; is_active?: boolean; region_group_id: string; home_port_location?: [number, number] } = req.body;
            const created_by = req.user!._id as mongoose.Types.ObjectId;
            const thumbnail_file = req.file;

            const vessel = await vesselService.create({
                name,
                thumbnail_file,
                unit_id,
                is_active,
                created_by,
                region_group_id,
                home_port_location,
            });

            res.status(201).json(vessel);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.put(
    "/:id",
    assignEndpointId.bind(this, endpointIds.UPDATE_VESSEL_MANAGEMENT),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageVessels]),
    restrictEndpointByUser,
    upload.single("thumbnail_file"),
    handleMulterError,
    validateData.bind(this, [
        param("id").isMongoId().withMessage("Invalid vessel ID"),
        body("name").optional().isString().trim().notEmpty().withMessage("Name must be a non-empty string"),
        body("unit_id").optional().isString().withMessage("Unit ID must be a string"),
        body("is_active").optional().isBoolean().withMessage("is_active must be a boolean"),
        body("remove_thumbnail").optional().isBoolean().withMessage("remove_thumbnail must be a boolean"),
        body("region_group_id").optional().isMongoId().withMessage("Region group ID must be a valid Object ID"),
        body("home_port_location")
            .optional({ nullable: true })
            .customSanitizer((value: string | number[] | null) => {
                if (value === null || value === undefined || value === "") return null;
                try {
                    return typeof value === "string" ? JSON.parse(value) : value;
                } catch (error) {
                    console.error("Error parsing home port location:", error);
                    throw new Error("Invalid home port coordinates format");
                }
            })
            .custom((value: number[] | null) => {
                if (value === null) return true;
                if (!Array.isArray(value)) {
                    throw new Error("Home port location must be an array");
                }
                if (value.length !== 2) {
                    throw new Error("Home port location must be an array with exactly 2 numbers");
                }
                if (!value.every((num: number) => typeof num === "number" && !isNaN(num))) {
                    throw new Error("Home port location must contain valid numbers");
                }
                return true;
            }),
    ]),
    async (req: Request, res: Response) => {
        try {
            const { id } = req.params;
            const {
                name,
                unit_id,
                is_active,
                remove_thumbnail,
                region_group_id,
                home_port_location,
            }: {
                name?: string;
                unit_id?: string;
                is_active?: boolean;
                remove_thumbnail?: boolean;
                region_group_id?: string;
                home_port_location?: [number, number] | null;
            } = req.body;
            const thumbnail_file = req.file;

            const vessel = await vesselService.update({
                id,
                name,
                thumbnail_file,
                unit_id,
                is_active,
                remove_thumbnail: remove_thumbnail ? "true" : undefined,
                region_group_id,
                home_port_location: home_port_location || undefined,
            });

            res.json(vessel);
        } catch (err) {
            validateError(err, res);
        }
    },
);

export default router;
