import express, { Request, Response } from "express";
import { param } from "express-validator";
import { endpointIds } from "../utils/endpointIds";
import isAuthenticated from "../middlewares/auth";
import { validateData } from "../middlewares/validator";
import assignEndpointId from "../middlewares/assignEndpointId";
import thingsboardService from "../services/Thingsboard.service";
import { validateError } from "../utils/functions";
import { IThingsboardDevice } from "../interfaces/Thingsboard";

const router = express.Router();

router.get("/devices", assignEndpointId.bind(this, endpointIds.FETCH_THINGSBOARD_DEVICES), isAuthenticated, async (_req: Request, res: Response) => {
    try {
        const devices: IThingsboardDevice[] = await thingsboardService.getAllDevices();
        res.json(devices);
    } catch (err) {
        validateError(err, res);
    }
});

router.get(
    "/device/:unitId",
    assignEndpointId.bind(this, endpointIds.FETCH_THINGSBOARD_DEVICE),
    isAuthenticated,
    validateData.bind(this, [
        param("unitId")
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req: Request, res: Response) => {
        try {
            const device: IThingsboardDevice | null = await thingsboardService.getDeviceByUnitId(req.params.unitId);
            if (!device) {
                return res.status(404).json({ message: "Device not found" });
            }
            res.json(device);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.get(
    "/reset-dashboards",
    assignEndpointId.bind(this, endpointIds.RESET_THINGSBOARD_DASHBOARDS),
    isAuthenticated,
    async (_req: Request, res: Response) => {
        try {
            thingsboardService.resetDashboards();
            res.json({ message: "Dashboards reset successful" });
        } catch (err) {
            validateError(err, res);
        }
    },
);

export default router;
