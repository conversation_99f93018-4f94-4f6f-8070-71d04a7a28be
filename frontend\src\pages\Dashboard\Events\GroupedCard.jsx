import { Grid, Typography, Tooltip, IconButton, Box, alpha, CircularProgress, Skeleton } from "@mui/material";
import { memo, useMemo, useEffect, useState, useCallback } from "react";
import { ChevronLeft, ChevronRight } from "@mui/icons-material";
import dayjs from "dayjs";
import { displayCoordinates, permissions, userValues } from "../../../utils";
import theme from "../../../theme";
import PreviewMedia from "../../../components/PreviewMedia";
import { useUser } from "../../../hooks/UserHook.jsx";
import useVesselInfo from "../../../hooks/VesselInfoHook.jsx";

const GroupedCard = ({ card, setShowDetailModal, setSelectedCard, buttonsToShow, signedUrls }) => {
    const { user } = useUser();
    const { vesselInfo } = useVesselInfo();

    const [currentIndex, setCurrentIndex] = useState(0);
    const [isImageLoading, setIsImageLoading] = useState(true);
    const [imageError, setImageError] = useState(false);
    const [src, setSrc] = useState(null);
    const [thumbnail, setThumbnail] = useState(null);

    // Memoized values
    const currentArtifact = useMemo(() => (card.isGroup ? card.groupArtifacts[currentIndex] : card), [card, currentIndex]);

    const totalCount = card.isGroup ? card.groupArtifacts.length : 1;
    const isVideo = Boolean(currentArtifact.video_path);
    const hasManageArtifacts = user?.hasPermissions([permissions.manageArtifacts]);

    const vessel = useMemo(
        () => vesselInfo.find((v) => v.vessel_id === currentArtifact.onboard_vessel_id),
        [vesselInfo, currentArtifact.onboard_vessel_id],
    );

    const vesselName = vessel?.name;

    const roundedCoordinates = useMemo(
        () => displayCoordinates(currentArtifact.location?.coordinates, !!user?.use_MGRS),
        [currentArtifact.location?.coordinates, user?.use_MGRS],
    );

    const handleClick = useCallback(() => {
        setShowDetailModal(true);
        setSelectedCard({
            ...currentArtifact,
            vesselName,
            isGroup: card.isGroup,
            groupArtifacts: card.groupArtifacts,
            currentGroupIndex: currentIndex,
        });
    }, [setShowDetailModal, setSelectedCard, currentArtifact, vesselName, card.isGroup, card.groupArtifacts, currentIndex]);

    // Optimized navigation handlers
    const navigate = useCallback(
        (direction) => {
            const newIndex = currentIndex + direction;
            if (newIndex < 0 || newIndex >= totalCount) return;

            setImageError(false);
            // Immediately change index, let image loading handle the rest
            setCurrentIndex(newIndex);
        },
        [currentIndex, totalCount],
    );

    const handlePrevious = useCallback(
        (e) => {
            e.stopPropagation();
            navigate(-1);
        },
        [navigate],
    );

    const handleNext = useCallback(
        (e) => {
            e.stopPropagation();
            navigate(1);
        },
        [navigate],
    );

    // Load current image only
    useEffect(() => {
        const thumbnailUrl = signedUrls.get(`${currentArtifact._id}:thumbnail`);

        if (!thumbnailUrl) {
            setIsImageLoading(false);
            setImageError(false);
            return;
        }

        setIsImageLoading(true);
        setImageError(false);
        setSrc(null);
        setThumbnail(null);

        const img = new Image();

        const handleLoad = () => {
            const currentUrl = signedUrls.get(`${currentArtifact._id}:thumbnail`);
            if (currentUrl === thumbnailUrl) {
                setThumbnail(thumbnailUrl);
                setSrc(thumbnailUrl);
                setIsImageLoading(false);
            }
        };

        const handleError = () => {
            const currentUrl = signedUrls.get(`${currentArtifact._id}:thumbnail`);
            if (currentUrl === thumbnailUrl) {
                setIsImageLoading(false);
                setImageError(true);
            }
        };

        img.onload = handleLoad;
        img.onerror = handleError;
        img.src = thumbnailUrl;

        return () => {
            img.onload = null;
            img.onerror = null;
        };
    }, [currentArtifact?._id, signedUrls]);

    if (!vesselInfo?.length) return <Typography>No vessel info</Typography>;

    const showImage = !isImageLoading && !imageError;
    const showNavigation = card.isGroup && totalCount > 1;

    return (
        <Grid
            container
            paddingTop="0 !important"
            height="100%"
            maxHeight="350px"
            className="events-step-2"
            onClick={handleClick}
            sx={{ cursor: "pointer" }}
        >
            <Grid container backgroundColor="primary.main" borderRadius={2} padding={1} gap={1}>
                <Grid size={12} height="200px" position="relative">
                    {/* Loading/Error states */}
                    {isImageLoading && (
                        <>
                            <Box
                                sx={{
                                    display: "flex",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    position: "absolute",
                                    top: 0,
                                    left: 0,
                                    width: "100%",
                                    height: "100%",
                                    zIndex: 1,
                                }}
                            >
                                <CircularProgress />
                            </Box>
                            <Skeleton
                                variant="rectangular"
                                width="100%"
                                height="100%"
                                sx={{
                                    borderRadius: 2,
                                    position: "absolute",
                                    top: 0,
                                    left: 0,
                                    right: 0,
                                    bottom: 0,
                                }}
                            />
                        </>
                    )}

                    {imageError && !isImageLoading && (
                        <Box
                            position="absolute"
                            top={0}
                            left={0}
                            right={0}
                            bottom={0}
                            display="flex"
                            alignItems="center"
                            justifyContent="center"
                            backgroundColor="rgba(0,0,0,0.2)"
                            borderRadius={2}
                            zIndex={2}
                        >
                            <Typography color="text.secondary" variant="body2">
                                Failed to load image
                            </Typography>
                        </Box>
                    )}

                    {/* Image */}
                    {showImage && (
                        <Box height="100%" width="100%">
                            <PreviewMedia
                                thumbnailLink={thumbnail}
                                originalLink={src}
                                cardId={currentArtifact._id}
                                isImage={!isVideo}
                                style={{ borderRadius: 8 }}
                                showVideoThumbnail={isVideo}
                                onThumbnailClick={handleClick}
                                showArchiveButton={hasManageArtifacts}
                                isArchived={currentArtifact?.portal?.is_archived || false}
                                vesselId={currentArtifact?.onboard_vessel_id}
                                buttonsToShow={buttonsToShow}
                                isGrouped={card.isGroup}
                                groupArtifacts={card.groupArtifacts}
                            />
                        </Box>
                    )}

                    {/* Navigation controls */}
                    {showNavigation && (
                        <Box
                            position="absolute"
                            bottom={8}
                            left="50%"
                            sx={{ transform: "translateX(-50%)", display: "flex", alignItems: "center", padding: "4px 8px", gap: 1, zIndex: 2 }}
                            onClick={(e) => e.stopPropagation()}
                        >
                            <IconButton
                                size="small"
                                onClick={handlePrevious}
                                disabled={currentIndex === 0}
                                sx={{
                                    color: "white",
                                    padding: "4px",
                                    background: alpha(theme.palette.custom.borderColor, 0.8) + " !important",
                                    "&:disabled": { color: "rgba(255,255,255,0.3)" },
                                }}
                            >
                                <ChevronLeft fontSize="small" />
                            </IconButton>

                            <Typography
                                variant="caption"
                                sx={{
                                    color: "white",
                                    fontWeight: 500,
                                    minWidth: "40px",
                                    textAlign: "center",
                                    padding: "5px 17px",
                                    borderRadius: "100px",
                                    background: alpha(theme.palette.primary.main, 0.8),
                                }}
                            >
                                {String(currentIndex + 1).padStart(2, "0")}/{String(totalCount).padStart(2, "0")}
                            </Typography>

                            <IconButton
                                size="small"
                                onClick={handleNext}
                                disabled={currentIndex === totalCount - 1}
                                sx={{
                                    color: "white",
                                    padding: "4px",
                                    background: alpha(theme.palette.custom.borderColor, 0.8) + " !important",
                                    "&:disabled": { color: "rgba(255,255,255,0.3)" },
                                }}
                            >
                                <ChevronRight fontSize="small" />
                            </IconButton>
                        </Box>
                    )}
                </Grid>
                <Grid container size={12}>
                    <Grid display="flex" justifyContent="space-between" alignItems="center" paddingX={1} size={12}>
                        <Tooltip title={vesselName?.length > 12 ? vesselName : ""}>
                            <Typography fontSize="14px" fontWeight={500}>
                                {vesselName?.length > 12 ? vesselName.slice(0, 12) + "..." : vesselName || "Unknown"}
                            </Typography>
                        </Tooltip>
                        <Typography fontSize="14px" fontWeight={500}>
                            {dayjs(currentArtifact.timestamp).format(userValues.dateTimeFormat(user, { exclude_seconds: true }))}
                        </Typography>
                    </Grid>
                    <Grid display="flex" justifyContent="space-between" alignItems="center" paddingX={1} size={12}>
                        <Typography fontSize="14px" fontWeight={500} color={theme.palette.custom.mainBlue}>
                            Location
                        </Typography>
                        <Typography fontSize="14px" fontWeight={500} color={theme.palette.custom.mainBlue}>
                            Category
                        </Typography>
                    </Grid>
                    <Grid display="flex" justifyContent="space-between" alignItems="center" paddingX={1} size={12}>
                        <Typography fontSize="14px" fontWeight={500} maxWidth="50%">
                            {roundedCoordinates}
                        </Typography>
                        <Typography fontSize="14px" fontWeight={500} maxWidth="50%" textAlign="right">
                            {currentArtifact.super_category?.length > 12
                                ? currentArtifact.super_category.slice(0, 12) + "..."
                                : currentArtifact.super_category || "Unspecified category"}
                        </Typography>
                    </Grid>
                </Grid>
            </Grid>
        </Grid>
    );
};

export default memo(GroupedCard);
