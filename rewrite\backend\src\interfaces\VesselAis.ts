import mongoose from "mongoose";
import { ICoordinates } from "./VesselLocation";

export interface IVesselAis {
    _id: mongoose.Types.ObjectId;
    location: ICoordinates;
    details: {
        message: Record<string, number | string | boolean>;
    };
    metadata: {
        onboard_vessel_id: mongoose.Types.ObjectId | string | null;
        unit_id: string;
        mmsi: string;
    };
    name: string | null;
    timestamp: Date;
}

export interface IAisLookup {
    _id: mongoose.Types.ObjectId;
    mmsi: string;
    collection: string;
    db: string;
    last_message_id: mongoose.Types.ObjectId;
    last_message_timestamp: Date;
    onboard_vessel_id: mongoose.Types.ObjectId;
    data: IVesselAis;
}
