import { jest, beforeEach, describe, it, expect } from '@jest/globals';
import { fetchGeolocation, googleMapsClient } from '../../modules/geolocation';

jest.mock('@googlemaps/google-maps-services-js', () => ({
    Client: jest.fn(() => ({
        reverseGeocode: jest.fn(),
    })),
    AddressType: {
        plus_code: 'plus_code',
    },
}));


describe('Geolocation Service Module', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Reverse Geocoding Functionality', () => {
        it('should return street address when valid geocoding data is available', async () => {
            const mockResponse = {
                data: {
                    results: [
                        { formatted_address: '123 Main St', types: ['street_address'] },
                        { formatted_address: '456 Other St', types: ['plus_code'] },
                    ],
                },
                status: 200,
            };

            (googleMapsClient.reverseGeocode as any).mockResolvedValueOnce(mockResponse);

            const result = await fetchGeolocation(40.7128, -74.0060);

            expect(result).toBe('123 Main St');
            expect(googleMapsClient.reverseGeocode).toHaveBeenCalled();
        });

        it('should return first available address when no street address is found', async () => {
            const mockResponse = {
                data: {
                    results: [
                        { formatted_address: '456 Other St', types: ['plus_code'] },
                    ],
                },
                status: 200,
            };

            (googleMapsClient.reverseGeocode as any).mockResolvedValueOnce(mockResponse);

            const result = await fetchGeolocation(40.7128, -74.0060);

            expect(result).toBe('456 Other St');
            expect(googleMapsClient.reverseGeocode).toHaveBeenCalled();
        });

        it('should throw error when no geocoding results are returned', async () => {
            const mockResponse = {
                data: {
                    results: [],
                },
                status: 200,
            };

            (googleMapsClient.reverseGeocode as any).mockResolvedValueOnce(mockResponse);

            await expect(fetchGeolocation(40.7128, -74.0060)).rejects.toThrow('No valid address found.');
            expect(googleMapsClient.reverseGeocode).toHaveBeenCalled();
        });

        it('should handle Google Maps API failures and propagate errors', async () => {
            (googleMapsClient.reverseGeocode as any).mockRejectedValueOnce(new Error('API call failed'));

            await expect(fetchGeolocation(40.7128, -74.0060)).rejects.toThrow('Google Maps Geocoding Error: API call failed');
            expect(googleMapsClient.reverseGeocode).toHaveBeenCalled();
        });

        it('should handle network timeout errors from Google Maps API', async () => {
            (googleMapsClient.reverseGeocode as any).mockRejectedValueOnce(new Error('Request timeout'));

            await expect(fetchGeolocation(40.7128, -74.0060)).rejects.toThrow('Google Maps Geocoding Error: Request timeout');
            expect(googleMapsClient.reverseGeocode).toHaveBeenCalled();
        });

        it('should fallback to "Unexpected error" when error has no message', async () => {
            (googleMapsClient.reverseGeocode as any).mockRejectedValueOnce('Some string reason');

            await expect(fetchGeolocation(40.7128, -74.0060)).rejects.toThrow('Google Maps Geocoding Error: Unexpected error');
            expect(googleMapsClient.reverseGeocode).toHaveBeenCalled();
        });

        it('should handle invalid coordinate parameters gracefully', async () => {
            const mockResponse = {
                data: {
                    results: [
                        { formatted_address: 'Unknown Location', types: ['unknown'] },
                    ],
                },
                status: 200,
            };

            (googleMapsClient.reverseGeocode as any).mockResolvedValueOnce(mockResponse);

            const result = await fetchGeolocation(999, 999);

            expect(result).toBe('Unknown Location');
            expect(googleMapsClient.reverseGeocode).toHaveBeenCalled();
        });
    });
});