import { Grid, Typography, Tooltip, Box, CircularProgress, Skeleton } from "@mui/material";
import { memo, useMemo, useEffect, useState, useCallback } from "react";
import dayjs from "dayjs";
import { displayCoordinates, permissions, userValues } from "../../../utils";
import theme from "../../../theme";
import PreviewMedia from "../../../components/PreviewMedia";
import { useUser } from "../../../hooks/UserHook.jsx";
import useVesselInfo from "../../../hooks/VesselInfoHook.jsx";
import s3Controller from "../../../controllers/S3.controller";

const UnifiedGroupCard = ({ card, setShowDetailModal, setSelectedCard, buttonsToShow, signedUrls }) => {
    const { user } = useUser();
    const { vesselInfo } = useVesselInfo();

    const [isImageLoading, setIsImageLoading] = useState(true);
    const [imageError, setImageError] = useState(false);
    const [src, setSrc] = useState(null);
    const [thumbnail, setThumbnail] = useState(null);
    // Memoized values
    const currentArtifact = card;

    const isVideo = Boolean(currentArtifact.video_path);
    const hasManageArtifacts = user?.hasPermissions([permissions.manageArtifacts]);

    const vessel = useMemo(
        () => vesselInfo.find((v) => v.vessel_id === currentArtifact.onboard_vessel_id),
        [vesselInfo, currentArtifact.onboard_vessel_id],
    );

    const vesselName = vessel?.name;

    const roundedCoordinates = useMemo(
        () => displayCoordinates(currentArtifact.location?.coordinates, !!user?.use_MGRS),
        [currentArtifact.location?.coordinates, user?.use_MGRS],
    );

    const handleClick = useCallback(() => {
        setShowDetailModal(true);
        setSelectedCard({
            ...currentArtifact,
            vesselName,
            isGroup: card.isGroup,
            groupArtifacts: card.groupArtifacts,
            currentGroupIndex: 0,
        });
    }, [setShowDetailModal, setSelectedCard, currentArtifact, vesselName, card.isGroup, card.groupArtifacts]);

    useEffect(() => {
        const loadImageUrl = async () => {
            if (!currentArtifact._id) {
                setIsImageLoading(false);
                setImageError(true);
                setSrc(null);
                setThumbnail(null);
                return;
            }

            try {
                setImageError(false);

                // Get thumbnail path from artifact
                const thumbnailPath = currentArtifact.thumbnail_path || currentArtifact.image_path;

                if (!thumbnailPath) {
                    setIsImageLoading(false);
                    setImageError(true);
                    setSrc(null);
                    setThumbnail(null);
                    return;
                }

                // Fetch signed URL using s3Controller
                const imageUrl = await s3Controller.fetchCloudfrontSignedUrl(
                    thumbnailPath,
                    currentArtifact.bucket_name,
                    currentArtifact.aws_region
                );

                if (imageUrl) {
                    // If the URL is the same as current src, don't reload
                    if (src === imageUrl && thumbnail === imageUrl) {
                        setIsImageLoading(false);
                        return;
                    }

                    // Check if image is already loaded in browser cache
                    const img = new Image();

                    const handleLoad = () => {
                        setThumbnail(imageUrl);
                        setSrc(imageUrl);
                        setIsImageLoading(false);
                    };

                    const handleError = () => {
                        setIsImageLoading(false);
                        setImageError(true);
                    };

                    if (img.complete || img.naturalWidth > 0) {
                        handleLoad();
                    } else {
                        setIsImageLoading(true);
                        setSrc(null);
                        setThumbnail(null);
                        img.onload = handleLoad;
                        img.onerror = handleError;
                    }

                    img.src = imageUrl;
                } else {
                    setIsImageLoading(false);
                    setImageError(true);
                    setSrc(null);
                    setThumbnail(null);
                }
            } catch (error) {
                console.error("Error loading image URL:", error);
                setIsImageLoading(false);
                setImageError(true);
                setSrc(null);
                setThumbnail(null);
            }
        };

        loadImageUrl();
    }, [currentArtifact._id, currentArtifact.thumbnail_path, currentArtifact.image_path, currentArtifact.bucket_name, currentArtifact.aws_region]);

    if (!vesselInfo?.length) return <Typography>No vessel info</Typography>;

    const showImage = !isImageLoading && !imageError;
    return (
        <Grid
            container
            paddingTop="0 !important"
            height="100%"
            // maxHeight="350px"
            className="events-step-2"
            onClick={handleClick}
            sx={{ cursor: "pointer" }}
        >
            <Grid container backgroundColor="primary.main" borderRadius={2} padding={1} gap={1}>
                <Grid size={12} height="200px" position="relative">
                    {/* Loading/Error states */}
                    {(isImageLoading) && (
                        <>
                            <Box
                                sx={{
                                    display: "flex",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    position: "absolute",
                                    top: 0,
                                    left: 0,
                                    width: "100%",
                                    height: "100%",
                                    zIndex: 1,
                                }}
                            >
                                <CircularProgress />
                            </Box>
                            <Skeleton
                                variant="rectangular"
                                width="100%"
                                height="100%"
                                sx={{
                                    borderRadius: 2,
                                    position: "absolute",
                                    top: 0,
                                    left: 0,
                                    right: 0,
                                    bottom: 0,
                                }}
                            />
                        </>
                    )}

                    {imageError && !isImageLoading && (
                        <Box
                            position="absolute"
                            top={0}
                            left={0}
                            right={0}
                            bottom={0}
                            display="flex"
                            alignItems="center"
                            justifyContent="center"
                            backgroundColor="rgba(0,0,0,0.2)"
                            borderRadius={2}
                            zIndex={2}
                        >
                            <Typography color="text.secondary" variant="body2">
                                Failed to load image
                            </Typography>
                        </Box>
                    )}

                    {/* Image */}
                    {showImage && (
                        <Box height="100%" width="100%">
                            <PreviewMedia
                                thumbnailLink={thumbnail}
                                originalLink={src}
                                cardId={currentArtifact._id}
                                isImage={!isVideo}
                                style={{ borderRadius: 8 }}
                                showVideoThumbnail={isVideo}
                                onThumbnailClick={handleClick}
                                showArchiveButton={hasManageArtifacts}
                                isArchived={currentArtifact?.portal?.is_archived || false}
                                vesselId={currentArtifact?.onboard_vessel_id}
                                buttonsToShow={buttonsToShow}
                                isGrouped={card.isGroup}
                                groupArtifacts={card.groupArtifacts}
                                isUnified={true}
                                unifiedArtifacts={card.duplications ? [currentArtifact, ...card.duplications] : [currentArtifact]}
                            />
                        </Box>
                    )}
                </Grid>
                <Grid container size={12}>
                    <Grid display="flex" justifyContent="space-between" alignItems="center" paddingX={1} size={12}>
                        <Tooltip title={vesselName?.length > 12 ? vesselName : ""}>
                            <Typography fontSize="14px" fontWeight={500}>
                                {vesselName?.length > 12 ? vesselName.slice(0, 12) + "..." : vesselName || "Unknown"}
                            </Typography>
                        </Tooltip>
                        <Typography fontSize="14px" fontWeight={500}>
                            {dayjs(currentArtifact.timestamp).format(userValues.dateTimeFormat(user, { exclude_seconds: true }))}
                        </Typography>
                    </Grid>
                    <Grid display="flex" justifyContent="space-between" alignItems="center" paddingX={1} size={12}>
                        <Typography fontSize="14px" fontWeight={500} color={theme.palette.custom.mainBlue}>
                            Location
                        </Typography>
                        <Typography fontSize="14px" fontWeight={500} color={theme.palette.custom.mainBlue}>
                            Category
                        </Typography>
                    </Grid>
                    <Grid display="flex" justifyContent="space-between" alignItems="center" paddingX={1} size={12}>
                        <Typography fontSize="14px" fontWeight={500} maxWidth="50%">
                            {roundedCoordinates}
                        </Typography>
                        <Typography fontSize="14px" fontWeight={500} maxWidth="50%" textAlign="right">
                            Multiple
                        </Typography>
                    </Grid>
                </Grid>
            </Grid>
        </Grid>
    );
};

export default memo(UnifiedGroupCard);
