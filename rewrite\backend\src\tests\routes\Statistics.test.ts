import request from 'supertest';
import app from '../../server';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } from '../data/Auth';
import User from '../../models/User';
import ApiKey from '../../models/ApiKey';
import { setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';
import { statisticsList } from '../data/Statistics';
import Statistics from '../../models/Statistics';
import { validateError } from '../mocks/utils/functions.mock';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../models/Statistics', () => require('../mocks/models/statistics.mock'));
jest.mock('../../utils/functions', () => require('../mocks/utils/functions.mock'));

describe('Statistics API', () => {
    describe('GET /api/statistics', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/statistics');
                    expect(res.status).toBe(401);
                });

                it('should return 400 for invalid type parameter', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/statistics?type=invalid')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 200 and fetch all statistics', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Statistics as any).find.mockResolvedValueOnce(statisticsList);

                    const res = await request(app)
                        .get('/api/statistics')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(statisticsList);
                    expect((Statistics as any).find).toHaveBeenCalledWith({}, {}, { sort: { fromTimestamp: -1 } });
                });

                it('should return 200 and fetch daily statistics', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const dailyStats = statisticsList.filter(stat => stat.type === 'daily');
                    (Statistics as any).find.mockResolvedValueOnce(dailyStats);

                    const res = await request(app)
                        .get('/api/statistics?type=daily')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(dailyStats);
                    expect((Statistics as any).find).toHaveBeenCalledWith({ type: 'daily' }, {}, { sort: { fromTimestamp: -1 } });
                });

                it('should return 200 and fetch weekly statistics', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const weeklyStats = statisticsList.filter(stat => stat.type === 'weekly');
                    (Statistics as any).find.mockResolvedValueOnce(weeklyStats);

                    const res = await request(app)
                        .get('/api/statistics?type=weekly')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(weeklyStats);
                    expect((Statistics as any).find).toHaveBeenCalledWith({ type: 'weekly' }, {}, { sort: { fromTimestamp: -1 } });
                });

                it('should return 200 with empty array when no statistics found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Statistics as any).find.mockResolvedValueOnce([]);

                    const res = await request(app)
                        .get('/api/statistics')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toEqual([]);
                });

                it('should return 500 if database error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Statistics as any).find.mockRejectedValueOnce(new Error('Database connection failed'));

                    const res = await request(app)
                        .get('/api/statistics')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });

                it('should handle type parameter with empty string', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/statistics?type=')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should handle multiple query parameters', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Statistics as any).find.mockResolvedValueOnce(statisticsList);

                    const res = await request(app)
                        .get('/api/statistics?type=daily&other=value')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(statisticsList);
                });

                it('should validate type parameter case sensitivity', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/statistics?type=Daily')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should handle non-string type parameter', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/statistics?type=123')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});