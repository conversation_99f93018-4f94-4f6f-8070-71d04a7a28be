import { jest, beforeEach, afterEach, describe, it, expect } from '@jest/globals';
import { sendOtp, verifyOtp, cleanupExpiredOtps, otpStore, startCleanupTimeout, stopCleanupTimeout, cleanupTimeout } from '../../modules/otpService';

describe('OTP Service Module', () => {
    const email = '<EMAIL>';
    const name = 'Test User';
    let sendEmail: jest.Mock;
    let mockSetTimeout: jest.Mock;
    let mockClearTimeout: jest.Mock;

    beforeEach(() => {
        jest.useFakeTimers();
        jest.clearAllMocks();
        otpStore.length = 0;
        sendEmail = jest.fn();

        mockSetTimeout = jest.fn((callback: Function, delay: number) => setTimeout(callback, delay)) as any;
        mockClearTimeout = jest.fn((timeout: NodeJS.Timeout) => clearTimeout(timeout)) as any;
    });

    afterEach(() => {
        jest.clearAllTimers();
        stopCleanupTimeout(mockClearTimeout);
    });

    describe('OTP Generation and Sending', () => {
        it('should successfully generate and send OTP via email', async () => {
            const response = await sendOtp(email, name, sendEmail as any);

            expect(response).toEqual({ message: 'OTP sent successfully' });
            expect(sendEmail).toHaveBeenCalledWith({
                to: email,
                subject: 'Your OTP Code',
                html: expect.stringContaining('OTP'),
            });
            expect(otpStore.length).toBe(1);
        });

        it('should handle email sending failures during OTP delivery', async () => {
            sendEmail.mockRejectedValueOnce(new Error('Failed to send OTP') as never);

            await expect(sendOtp(email, name, sendEmail as any)).rejects.toThrow('Failed to send OTP');
        });

        it('should generate unique OTP codes for multiple requests', async () => {
            const _firstResponse = await sendOtp(email, name, sendEmail as any);
            const _secondResponse = await sendOtp(email, name, sendEmail as any);

            expect(otpStore.length).toBe(2);
            expect(otpStore[0].otp).not.toBe(otpStore[1].otp);
        });
    });

    describe('OTP Verification Process', () => {
        it('should successfully verify valid OTP codes', () => {
            otpStore.push({
                email,
                otp: 100000,
                expiresAt: Date.now() + 60000,
            });
            const result = verifyOtp(email, 100000);
            expect(result).toEqual({ valid: true });
        });

        it('should reject invalid OTP codes with appropriate error message', () => {
            const result = verifyOtp(email, 100010);
            expect(result).toEqual({ valid: false, message: 'Invalid OTP or email' });
        });

        it('should reject expired OTP codes with expiration message', () => {
            otpStore.push({
                email,
                otp: 100000,
                expiresAt: Date.now() - 60000,
            });
            const result = verifyOtp(email, 100000);
            expect(result).toEqual({ valid: false, message: 'OTP expired' });
        });

        it('should handle verification for non-existent email addresses', () => {
            const result = verifyOtp('<EMAIL>', 100000);
            expect(result).toEqual({ valid: false, message: 'Invalid OTP or email' });
        });
    });

    describe('OTP Store Management and Cleanup', () => {
        it('should remove expired OTPs from the store during cleanup', () => {
            otpStore.push({
                email,
                otp: 100000,
                expiresAt: Date.now() - 60000,
            });
            cleanupExpiredOtps();

            expect(otpStore.length).toEqual(0);
        });

        it('should preserve valid OTPs in store after cleanup process', () => {
            otpStore.push({
                email,
                otp: 100000,
                expiresAt: Date.now() + 60000,
            });
            cleanupExpiredOtps();

            expect(otpStore.length).toEqual(1);
        });

        it('should handle cleanup with mixed valid and expired OTPs', () => {
            otpStore.push({
                email,
                otp: 100000,
                expiresAt: Date.now() + 60000,
            });
            otpStore.push({
                email: '<EMAIL>',
                otp: 200000,
                expiresAt: Date.now() - 60000,
            });
            cleanupExpiredOtps();

            expect(otpStore.length).toEqual(1);
            expect(otpStore[0].email).toBe(email);
        });
    });

    describe('Cleanup Timeout Management', () => {
        it('should initialize cleanup timeout only once when called multiple times', () => {
            startCleanupTimeout(mockSetTimeout as any);
            startCleanupTimeout(mockSetTimeout as any);

            expect(mockSetTimeout).toHaveBeenCalledTimes(1);
        });

        it('should reschedule cleanup timeout when valid OTPs remain after cleanup', () => {
            otpStore.push({
                email,
                otp: 123456,
                expiresAt: Date.now() + 60000,
            });
            startCleanupTimeout(mockSetTimeout as any);

            jest.runOnlyPendingTimers();

            expect(mockSetTimeout).toHaveBeenCalled();
        });

        it('should not reschedule cleanup when OTP store becomes empty', () => {
            otpStore.push({
                email,
                otp: 123456,
                expiresAt: Date.now() - 60000,
            });

            startCleanupTimeout(mockSetTimeout as any);
            cleanupExpiredOtps();

            jest.runOnlyPendingTimers();
            expect(mockSetTimeout).toHaveBeenCalledTimes(1);
        });

        it('should properly stop cleanup timeout when requested', () => {
            startCleanupTimeout(mockSetTimeout as any);
            stopCleanupTimeout();

            expect(cleanupTimeout).toBeNull();
        });

        it('should handle stop cleanup timeout with custom clearTimeout function', () => {
            startCleanupTimeout(mockSetTimeout as any);
            stopCleanupTimeout(mockClearTimeout);

            expect(cleanupTimeout).toBeNull();
        });
    });
});
