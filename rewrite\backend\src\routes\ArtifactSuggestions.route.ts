import express, { Request, Response } from "express";
import natural from "natural";
import SpellingCorrector from "../modules/spellingCorrector";
import ArtifactSuggestion from "../models/ArtifactSuggestion";
import ArtifactSynonym from "../models/ArtifactSynonym";
import { cleanSuggestion, consoleLogObjectSize } from "../utils/functions";
import { IArtifactSuggestion, IArtifactSynonym, ISuggestionResult } from "../interfaces/ArtifactSuggestion";

const router = express.Router();

const wordnet = new natural.WordNet();
const spelling = new SpellingCorrector();
spelling.loadDictionary();

let lastSynonymCheckedAt: Date | null = null;
const synonymCache: { synonyms: Record<string, string> | null } = { synonyms: null };
const synonymCachePeriodMs: number = 300000; // 5 minutes
const SUGGESTION_LIMIT: number = 10;

const getSynonyms = (word: string): Promise<string[]> => {
    return new Promise((resolve) => {
        wordnet.lookup(word, (results: { synonyms: string[] }[]) => {
            const synonyms = new Set<string>();
            results.forEach((result: { synonyms: string[] }) => {
                result.synonyms.forEach((syn: string) => {
                    if (syn.toLowerCase() !== word.toLowerCase()) {
                        synonyms.add(syn);
                    }
                });
            });
            resolve(Array.from(synonyms).slice(0, 5));
        });
    });
};

function rankSuggestions(suggestionsArr: string[], suggestionDocs: IArtifactSuggestion[]): string[] {
    const docMap = new Map<string, IArtifactSuggestion>(suggestionDocs.map((s: IArtifactSuggestion) => [s.search, s]));
    let ranked: string[] = suggestionsArr
        .map((s: string) => {
            const doc = docMap.get(s);
            return {
                suggestion: s,
                click: doc ? doc.click : 0,
            };
        })
        .sort((a: ISuggestionResult, b: ISuggestionResult) => {
            const aScore = a.click;
            const bScore = b.click;
            if (bScore !== aScore) return bScore - aScore;
            return a.suggestion.localeCompare(b.suggestion);
        })
        .map((r: ISuggestionResult) => r.suggestion);
    return ranked;
}

function generateCombinations(wordAlternatives: string[][], limit: number): string[] {
    const out: string[] = [];
    function combine(arr: string[][], prefix: string[] = []): void {
        if (out.length >= limit) return;
        if (arr.length === 0) {
            out.push(prefix.join(" "));
            return;
        }
        for (let i = 0; i < arr[0].length; i++) {
            combine(arr.slice(1), [...prefix, arr[0][i]]);
            if (out.length >= limit) break;
        }
    }
    combine(wordAlternatives);
    return out;
}

const getSynonymMap = async (): Promise<Record<string, string>> => {
    if (!lastSynonymCheckedAt || !synonymCache.synonyms || Date.now() - lastSynonymCheckedAt.getTime() >= synonymCachePeriodMs) {
        const allSynonyms = (await ArtifactSynonym.find({}, { word: 1, synonyms: 1 })) as IArtifactSynonym[];
        const synonymMap: Record<string, string> = {};
        for (const syn of allSynonyms) {
            if (Array.isArray(syn.synonyms)) {
                for (const s of syn.synonyms) {
                    synonymMap[s.toLowerCase()] = syn.word;
                }
            }
            synonymMap[syn.word.toLowerCase()] = syn.word;
        }
        synonymCache.synonyms = synonymMap;
        lastSynonymCheckedAt = new Date();
    }
    return synonymCache.synonyms!;
};

function normalizeSuggestionsArray(arr: string[], synonymMap: Record<string, string>): string[] {
    const normalized: string[] = arr.map((str: string) =>
        str
            .split(/\s+/)
            .map((word: string) => (synonymMap[word.toLowerCase()] || word).toLowerCase())
            .join(" ")
            .trim(),
    );
    return Array.from(new Set(normalized.filter(Boolean)));
}

router.post("/", async (req: Request, res: Response) => {
    try {
        // 1. Validate and clean the query
        const { query } = req.body;
        if (!query || typeof query !== "string") {
            return res.status(400).json({ message: "Query is required" });
        }
        const cleanQuery = cleanSuggestion(query);
        const synonymMap = await getSynonymMap();

        // 2. For each word, get alternatives: synonym, spell-correct, or WordNet synonyms
        const words: string[] = cleanQuery.split(/\s+/);
        const wordAlternatives: string[][] = await Promise.all(
            words.map(async (word: string) => {
                const lower: string = word.toLowerCase();
                if (synonymMap[lower]) return [synonymMap[lower]];
                if (word && word.split(/\s+/).length === 1) {
                    const correction: string = spelling.correct(word);
                    if (correction && correction !== word) {
                        return [correction];
                    }
                }
                const syns: string[] = await getSynonyms(word);
                return [word, ...syns];
            }),
        );
        let generated: string[] = generateCombinations(wordAlternatives, 20).map(cleanSuggestion).filter(Boolean);

        // 3. Find DB suggestions (partial match, case-insensitive)
        const regex: RegExp = new RegExp(words.map((w: string) => w.toLowerCase()).join("|"), "i");
        const dbSuggestions: IArtifactSuggestion[] = (await ArtifactSuggestion.find({ search: { $regex: regex } })) as IArtifactSuggestion[];
        const dbNorm: string[] = dbSuggestions.map((s: IArtifactSuggestion) => s.search);

        // 4. Merge, normalize, and dedupe all suggestions
        let suggestions: string[] = normalizeSuggestionsArray([...generated, ...dbNorm], synonymMap);

        // 5. Rank suggestions
        suggestions = rankSuggestions(suggestions, dbSuggestions);

        // 6. Always show the normalized/corrected query at the top
        const normalizedQuery: string = wordAlternatives
            .map((a: string[]) => a[0])
            .join(" ")
            .toLowerCase();
        suggestions = [normalizedQuery, ...suggestions.filter((s: string) => s.toLowerCase() !== normalizedQuery)];

        // 7. Limit suggestions before updating impressions and returning
        suggestions = suggestions.map((s: string) => s.toLowerCase()).slice(0, SUGGESTION_LIMIT);
        const limitedDbNorm: string[] = suggestions.filter((s: string) => dbNorm.map((x: string) => x.toLowerCase()).includes(s));

        // 8. Update impressions for found suggestions only
        if (limitedDbNorm.length > 0) {
            ArtifactSuggestion.updateMany({ search: { $in: limitedDbNorm } }, { $inc: { impressions: 1 } }).catch(console.error);
        }

        return res.json({ suggestions });
    } catch {
        return res.status(500).json({ message: "Internal server error" });
    }
});

if (process.env.NODE_ENV !== "test") {
    setInterval(() => {
        consoleLogObjectSize(synonymCache, "artifactSuggestions.synonymCache");
    }, 60000); // 1 minute
}
export default router;
