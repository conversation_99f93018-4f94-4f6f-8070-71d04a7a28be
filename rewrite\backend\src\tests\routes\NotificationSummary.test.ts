import User from '../../models/User';
import { generateUserToken, authorizedUser, nonAuthorizedUser } from '../data/Auth';
import NotificationSummary from '../../models/NotificationSummary';
import request from 'supertest';
import app from '../../server';
import { jest, describe, it, beforeEach, expect, afterEach } from '@jest/globals';
import { setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';
import { canAccessVessel, validateError } from '../../utils/functions';
import vesselService from '../../services/Vessel.service';
import EmailDomains from '../../models/EmailDomains';
import jwt from 'jsonwebtoken';
import ApiKey from '../../models/ApiKey';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';
process.env.API_URL = 'http://localhost:3000';
process.env.APP_URL = 'http://localhost:3001';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../models/NotificationSummary', () => require('../mocks/models/notificationSummary.mock'));
jest.mock('../../models/EmailDomains', () => require('../mocks/models/emailDomains.mock'));
jest.mock('../../services/Vessel.service', () => require('../mocks/services/vesselService.mock'));
jest.mock('../../utils/functions', () => require('../mocks/utils/functions.mock'));
jest.mock('../../modules/email', () => require('../mocks/modules/email.mock'));

describe('Notification Summary API', () => {
    describe('GET /api/summaryReports', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, user) => {
            const _nonAuthToken = 'Bearer ' + generateToken(user.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(user.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/summaryReports');
                    expect(res.status).toBe(401);
                });

                it('should return 200 and fetch summaries if authorized', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (NotificationSummary.countDocuments as jest.Mock).mockResolvedValue(5 as never);
                    (NotificationSummary.find as jest.Mock).mockReturnValue({
                        skip: jest.fn().mockReturnThis(),
                        limit: jest.fn().mockReturnThis(),
                        exec: jest.fn().mockResolvedValue([{}] as never)
                    });

                    const res = await request(app).get('/api/summaryReports').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('data');
                    expect(res.body).toHaveProperty('total_pages');
                });

                it('should return 200 with pagination fields computed', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (NotificationSummary.countDocuments as jest.Mock).mockResolvedValue(100 as never);
                    (NotificationSummary.find as jest.Mock).mockReturnValue({
                        skip: jest.fn().mockReturnThis(),
                        limit: jest.fn().mockReturnThis(),
                        exec: jest.fn().mockResolvedValue([] as never)
                    });

                    const res = await request(app).get('/api/summaryReports?page=2&page_size=10').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('next_page');
                    expect(res.body).toHaveProperty('previous_page');
                });

                it('should return 500 on internal error', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (NotificationSummary.countDocuments as jest.Mock).mockRejectedValue(new Error('db') as never);
                    const res = await request(app).get('/api/summaryReports').set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/summaryReports/:id', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, user) => {
            const _nonAuthToken = 'Bearer ' + generateToken(user.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(user.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/summaryReports/507f1f77bcf86cd799439011');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if invalid id is provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (NotificationSummary.findOne as jest.Mock).mockResolvedValue(null as never);
                    const res = await request(app).get('/api/summaryReports/321315').set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 404 if not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (NotificationSummary.findOne as jest.Mock).mockResolvedValue(null as never);
                    const res = await request(app).get('/api/summaryReports/507f1f77bcf86cd799439011').set('Authorization', authToken);
                    expect(res.status).toBe(404);
                });

                it('should return 200 if found', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (NotificationSummary.findOne as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011' } as never);
                    const res = await request(app).get('/api/summaryReports/507f1f77bcf86cd799439011').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should return 500 on internal error', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (NotificationSummary.findOne as jest.Mock).mockRejectedValue(new Error('db') as never);
                    const res = await request(app).get('/api/summaryReports/507f1f77bcf86cd799439011').set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('PATCH /api/summaryReports/:id', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, user) => {
            const _nonAuthToken = 'Bearer ' + generateToken(user.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(user.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                    (canAccessVessel as jest.Mock).mockReturnValue(true as never);
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011');
                    expect(res.status).toBe(401);
                });

                it('should return 400 for invalid id', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    const res = await request(app).patch('/api/summaryReports/invalid-id').set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if vessel_ids is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ vessel_ids: null });
                    const res2 = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ vessel_ids: [] });
                    expect([res.status, res2.status]).toContain(400);
                });

                it('should return 400 if preference is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ preference: null });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if is_enabled is 1', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ is_enabled: 1 });
                    expect(res.status).toBe(404);
                });

                it('should return 400 if title is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ preference: ["daily"], title: null });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if receivers is more than 20', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ receivers: Array(21).fill('<EMAIL>') });
                    expect(res.status).toBe(400);
                });

                it('should return 403 for unauthorized vessel ids', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (vesselService.find as jest.Mock).mockResolvedValue([{ _id: '507f1f77bcf86cd799439012' }] as never);
                    (canAccessVessel as jest.Mock).mockReturnValue(false as never);
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011').set('Authorization', authToken).send({ vessel_ids: ['507f1f77bcf86cd799439012'] });
                    expect(res.status).toBe(403);
                });

                it('should return 400 for invalid vessel_ids.*', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ vessel_ids: ['invalid-object-id'] });
                    expect(res.status).toBe(400);
                });

                it('should return 400 when preference has invalid values', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ preference: ['invalid'] });
                    expect(res.status).toBe(400);
                });

                it('should return 400 when receivers is not array', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ receivers: 'invalid' });
                    expect(res.status).toBe(400);
                });

                it('should return 200 and update summary', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (NotificationSummary.findOneAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011' } as never);
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011').set('Authorization', authToken).send({ title: ['A'], preference: ['daily'] });
                    expect(res.status).toBe(200);
                });

                it('should return 404 when update returns null', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (NotificationSummary.findOneAndUpdate as jest.Mock).mockResolvedValue(null as never);
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011').set('Authorization', authToken).send({ title: ['A'] });
                    expect(res.status).toBe(404);
                });

                it('should handle receivers validations and sending flow', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([{ domain: 'example.com' }] as never);
                    (NotificationSummary.findById as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011', receivers: [], title: ['T'], preference: ['daily'], created_by: user.authorized._id } as never);
                    (NotificationSummary.findOneAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011' } as never);
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011').set('Authorization', authToken).send({ receivers: ['<EMAIL>'] });
                    expect([200, 500]).toContain(res.status);
                });

                it('should return 400 when user has no email but receivers provided', async () => {
                    const mockUser = { ...user.authorized, email: null };
                    setupAuthorizedAuthMocks(authMethod, { authorized: mockUser }, authToken, User, ApiKey);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([{ domain: 'example.com' }] as never);
                    (NotificationSummary.findById as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011', receivers: [], title: ['T'], preference: ['daily'], created_by: mockUser._id } as never);
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011').set('Authorization', authToken).send({ receivers: ['<EMAIL>'] });
                    expect(res.status).toBe(400);
                });

                it('should return 404 when notification summary not found', async () => {
                    const mockUser = { ...user.authorized, permissions: [] };
                    setupAuthorizedAuthMocks(authMethod, { authorized: mockUser }, authToken, User, ApiKey);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([{ domain: 'example.com' }] as never);
                    (NotificationSummary.findById as jest.Mock).mockResolvedValue(null as never);
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011').set('Authorization', authToken).send({ receivers: ['<EMAIL>'] });
                    expect(res.status).toBe(404);
                });

                it('should return 200 when receivers are valid', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([{ domain: 'example.com' }] as never);
                    (NotificationSummary.findById as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011', receivers: [], title: null, preference: null, created_by: user.authorized._id } as never);
                    (NotificationSummary.findOneAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011' } as never);
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011').set('Authorization', authToken).send({ receivers: ['<EMAIL>'] });
                    expect(res.status).toBe(200);
                });

                it('should return 400 when user dont have permission but receivers provided', async () => {
                    const mockUser = { ...user.authorized, permissions: [] };
                    setupAuthorizedAuthMocks(authMethod, { authorized: mockUser }, authToken, User, ApiKey);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([{ domain: 'example.com' }] as never);
                    (NotificationSummary.findById as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011', receivers: [], title: ['T'], preference: ['daily'], created_by: mockUser._id } as never);
                    (NotificationSummary.findOneAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011' } as never);
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011').set('Authorization', authToken).send({ receivers: ['<EMAIL>', '<EMAIL>'] });
                    expect(res.status).toBe(400);
                });

                it('should return 400 when user email domain is not allowed', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([{ domain: 'allowed.com' }] as never);
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ receivers: ['<EMAIL>'] });
                    expect(res.status).toBe(400);
                });

                it('should return 400 when receivers contain invalid email', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ receivers: ['bad-email'] });
                    expect(res.status).toBe(400);
                });

                it('should return 400 when title is empty array', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ title: [] });
                    expect(res.status).toBe(400);
                });

                it('should return 400 when is_enabled has invalid value', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ is_enabled: 3 });
                    expect(res.status).toBe(400);
                });

                it('should return 200 when vessel_ids contains "all"', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (vesselService.find as jest.Mock).mockResolvedValue([] as never);
                    (NotificationSummary.findOneAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1' } as never);
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ vessel_ids: ['all'] });
                    expect(res.status).toBe(200);
                });

                it('should return 500 on internal error', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (NotificationSummary.findOneAndUpdate as jest.Mock).mockRejectedValue(new Error('db') as never);
                    const res = await request(app).patch('/api/summaryReports/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ title: ['A'] });
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('DELETE /api/summaryReports/:id', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, user) => {
            const _nonAuthToken = 'Bearer ' + generateToken(user.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(user.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).delete('/api/summaryReports/507f1f77bcf86cd799439011');
                    expect(res.status).toBe(401);
                });

                it('should return 400 for invalid id', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    const res = await request(app).delete('/api/summaryReports/invalid-id').set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 200 and delete summary', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (NotificationSummary.findOneAndDelete as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011' } as never);
                    const res = await request(app).delete('/api/summaryReports/507f1f77bcf86cd799439011').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should return 404 when delete returns null', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (NotificationSummary.findOneAndDelete as jest.Mock).mockResolvedValue(null as never);
                    const res = await request(app).delete('/api/summaryReports/507f1f77bcf86cd799439011').set('Authorization', authToken);
                    expect(res.status).toBe(404);
                });

                it('should return 500 on internal error', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (NotificationSummary.findOneAndDelete as jest.Mock).mockRejectedValue(new Error('db') as never);
                    const res = await request(app).delete('/api/summaryReports/507f1f77bcf86cd799439011').set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/summaryReports', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, user) => {
            const _nonAuthToken = 'Bearer ' + generateToken(user.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(user.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (canAccessVessel as jest.Mock).mockReturnValue(true as never);
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/summaryReports');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if receivers is empty', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    const res = await request(app).post('/api/summaryReports').set('Authorization', authToken).send({ receivers: null });
                    const res2 = await request(app).post('/api/summaryReports').set('Authorization', authToken).send({ receivers: Array(21).fill('<EMAIL>') });
                    const res3 = await request(app).post('/api/summaryReports').set('Authorization', authToken).send({ receivers: 'invalid' });
                    expect([res.status, res2.status, res3.status]).toContain(400);
                });

                it('should return 400 if is_enabled is empty', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    const res = await request(app).post('/api/summaryReports').set('Authorization', authToken).send({ is_enabled: 3 });
                    const res2 = await request(app).post('/api/summaryReports').set('Authorization', authToken).send({ is_enabled: 0 });
                    expect([res.status, res2.status]).toContain(400);
                });

                it('should return 400 if title is empty', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    const res = await request(app).post('/api/summaryReports').set('Authorization', authToken).send({ title: [] });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if vessel_ids is empty', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    const res = await request(app).post('/api/summaryReports').set('Authorization', authToken).send({ vessel_ids: [] });
                    const res2 = await request(app).post('/api/summaryReports').set('Authorization', authToken).send({ vessel_ids: ["invalid"] });
                    expect([res.status, res2.status]).toContain(400);
                });

                it('should return 400 on validation error', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    const res = await request(app).post('/api/summaryReports').set('Authorization', authToken).send({});
                    expect(res.status).toBe(400);
                });

                it('should return 403 for unauthorized vessels', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (vesselService.find as jest.Mock).mockResolvedValue([{ _id: '507f1f77bcf86cd799439012' }] as never);
                    (canAccessVessel as jest.Mock).mockReturnValue(false as never);
                    const res = await request(app).post('/api/summaryReports').set('Authorization', authToken).send({ vessel_ids: ['507f1f77bcf86cd799439012'], preference: ['daily'], title: ['A'] });
                    expect(res.status).toBe(403);
                });

                it('should return 400 when user has no email but receivers provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([{ domain: 'example.com' }] as never);
                    (vesselService.find as jest.Mock).mockResolvedValue([] as never);
                    (User.findById as jest.Mock).mockResolvedValue({ _id: user.authorized._id, email: null } as never);
                    const res = await request(app).post('/api/summaryReports').set('Authorization', authToken).send({ vessel_ids: ['all'], preference: ['daily'], receivers: ['<EMAIL>'], title: ['A'] });
                    expect([400, 500]).toContain(res.status);
                });

                it('should return 400 if user has no email and receivers provided', async () => {
                    const mockUser = { ...user.authorized, email: null };
                    setupAuthorizedAuthMocks(authMethod, { authorized: mockUser }, authToken, User, ApiKey);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([{ domain: 'example.com' }] as never);
                    (vesselService.find as jest.Mock).mockResolvedValue([] as never);
                    const res = await request(app).post('/api/summaryReports').set('Authorization', authToken).send({ vessel_ids: ['all'], preference: ['daily'], receivers: ['<EMAIL>'], is_enabled: 1, title: ['A'] });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if user email domain is not allowed', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([{ domain: 'allowed.com' }] as never);
                    (vesselService.find as jest.Mock).mockResolvedValue([] as never);
                    const res = await request(app).post('/api/summaryReports').set('Authorization', authToken).send({ vessel_ids: ['all'], preference: ['daily'], receivers: ['<EMAIL>'], is_enabled: 1, title: ['A'] });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if user dont have permission to add additional email addresses', async () => {
                    const mockUser = { ...user.authorized, permissions: [] };
                    setupAuthorizedAuthMocks(authMethod, { authorized: mockUser }, authToken, User, ApiKey);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([{ domain: 'example.com' }] as never);
                    (vesselService.find as jest.Mock).mockResolvedValue([] as never);
                    const res = await request(app).post('/api/summaryReports').set('Authorization', authToken).send({ vessel_ids: ['all'], preference: ['daily'], receivers: ['<EMAIL>'], is_enabled: 1, title: ['A'] });
                    expect(res.status).toBe(400);
                });

                it('should create summary with is_enabled=1 and send emails', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([{ domain: 'example.com' }] as never);
                    (vesselService.find as jest.Mock).mockResolvedValue([] as never);
                    (NotificationSummary.create as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011', title: null, preference: null } as never);
                    const res = await request(app).post('/api/summaryReports').set('Authorization', authToken).send({ vessel_ids: ['132323232323232323232323'], preference: ['daily'], receivers: ['<EMAIL>'], is_enabled: 0, title: ['A'] });
                    expect([201, 500]).toContain(res.status);
                });

                it('should create summary with is_enabled=1 and send emails', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([{ domain: 'example.com' }] as never);
                    (vesselService.find as jest.Mock).mockResolvedValue([] as never);
                    (NotificationSummary.create as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011', title: ['A'], preference: ['daily'] } as never);
                    const res = await request(app).post('/api/summaryReports').set('Authorization', authToken).send({ vessel_ids: ['all'], preference: ['daily'], receivers: ['<EMAIL>'], is_enabled: 1, title: ['A'] });
                    expect([201, 500]).toContain(res.status);
                });

                it('should throw error if create throws error', async () => {
                    setupAuthorizedAuthMocks(authMethod, user, authToken, User, ApiKey);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([{ domain: 'example.com' }] as never);
                    (vesselService.find as jest.Mock).mockResolvedValue([] as never);
                    (NotificationSummary.create as jest.Mock).mockRejectedValue(new Error('db') as never);
                    const res = await request(app).post('/api/summaryReports').set('Authorization', authToken).send({ vessel_ids: ['all'], preference: ['daily'], receivers: ['<EMAIL>'], is_enabled: 1, title: ['A'] });
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/summaryReports/unsubscribe/email', () => {
        let verifySpy: ReturnType<typeof jest.spyOn>;
        beforeEach(() => {
            jest.resetAllMocks();
            verifySpy = jest.spyOn(jwt as any, 'verify').mockImplementation((...args: unknown[]) => {
                const token = args[0] as string;
                try { return JSON.parse(token); } catch { return {}; }
            });
        });

        afterEach(() => { verifySpy.mockRestore(); });

        it('should return 400 if token is missing', async () => {
            const res = await request(app).get('/api/summaryReports/unsubscribe/email');
            expect(res.status).toBe(400);
        });

        it('should redirect 404 if summary not found', async () => {
            (NotificationSummary.findById as jest.Mock).mockResolvedValue(null as never);
            const res = await request(app).get('/api/summaryReports/unsubscribe/email').query({ token: JSON.stringify({ notificationId: '507f1', email: '<EMAIL>' }) });
            expect(res.status).toBe(302);
        });

        it('should 302 and disable if requester is creator', async () => {
            (NotificationSummary.findById as jest.Mock).mockResolvedValue({ _id: '507f1', receivers: ['<EMAIL>'], created_by: authorizedUser._id } as never);
            (User.findOne as jest.Mock).mockResolvedValue({ _id: authorizedUser._id, email: '<EMAIL>' } as never);
            (NotificationSummary.updateOne as jest.Mock).mockResolvedValue({ modifiedCount: 1 } as never);
            const res = await request(app).get('/api/summaryReports/unsubscribe/email').query({ token: JSON.stringify({ notificationId: '507f1', email: '<EMAIL>' }) });
            expect(res.status).toBe(302);
        });

        it('should 302 when email not in receivers and user is not creator', async () => {
            (NotificationSummary.findById as jest.Mock).mockResolvedValue({ _id: '507f1', receivers: ['<EMAIL>'], created_by: authorizedUser._id } as never);
            (User.findOne as jest.Mock).mockResolvedValue(null as never);
            const res = await request(app).get('/api/summaryReports/unsubscribe/email').query({ token: JSON.stringify({ notificationId: '507f1', email: '<EMAIL>' }) });
            expect(res.status).toBe(302);
        });

        it('should 302 remove receiver when present', async () => {
            (NotificationSummary.findById as jest.Mock).mockResolvedValue({ _id: '507f1', receivers: ['<EMAIL>'], created_by: authorizedUser._id } as never);
            (NotificationSummary.updateOne as jest.Mock).mockResolvedValue({ modifiedCount: 1 } as never);
            const res = await request(app).get('/api/summaryReports/unsubscribe/email').query({ token: JSON.stringify({ notificationId: '507f1', email: '<EMAIL>' }) });
            expect(res.status).toBe(302);
        });

        it('should 302 when updateOne modifies nothing (receiver absent)', async () => {
            (NotificationSummary.findById as jest.Mock).mockResolvedValue({ _id: '507f1', receivers: ['<EMAIL>'], created_by: authorizedUser._id } as never);
            (NotificationSummary.updateOne as jest.Mock).mockResolvedValue({ modifiedCount: 0 } as never);
            const res = await request(app).get('/api/summaryReports/unsubscribe/email').query({ token: JSON.stringify({ notificationId: '507f1', email: '<EMAIL>' }) });
            expect(res.status).toBe(302);
        });

        it('should 500 on internal error', async () => {
            (NotificationSummary.findById as jest.Mock).mockRejectedValue(new Error('db') as never);
            const res = await request(app).get('/api/summaryReports/unsubscribe/email').query({ token: JSON.stringify({ notificationId: '507f1', email: '<EMAIL>' }) });
            expect(res.status).toBe(500);
        });
    });
});


