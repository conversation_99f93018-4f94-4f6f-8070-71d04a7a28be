require("dotenv").config();
require("aws-sdk/lib/maintenance_mode_message").suppress = true;
require("./modules/processLogs");
// TODO: fix imports
const express = require("express");
const mongoose = require("mongoose");
const cors = require("cors");
const path = require("path");
const socketIo = require("socket.io");
const http = require("http");
const ioEmitter = require("./modules/ioEmitter");
const jwt = require("jsonwebtoken");
const SessionLog = require("./models/SessionLog");
const { swaggerUi, swaggerDocs, swaggerConfig } = require("./modules/swagger");
const cookieParser = require("cookie-parser");
const { v4: uuidv4 } = require("uuid");
const redisClient = require("./services/Redis.service");

// import userRouter from './routes/User';
// import roleRouter from './routes/Role';
// import kinesisRouter from './routes/Kinesis';
// import regionRouter from './routes/Region';
// import vesselLocationRouter from './routes/VesselLocation';
// import permissionRouter from './routes/Permission';
// import artifactRouter from './routes/Artifact';
// import logRouter from './routes/Log';
// import s3Router from './routes/S3';
// import apiKeyRouter from './routes/ApiKey';
// import apiEndpointRouter from './routes/ApiEndpoint';
// import statisticsRouter from './routes/Statistics';
// import vesselRouter from './routes/Vessels';
// import geolocationRouter from './routes/Geolocation';
// import tourGuideRouter from './routes/TourGuide';
// import notificationAlertRouter from './routes/NotificationAlert';
// import inAppNotificationRouter from './routes/InAppNotification';

const app = express();
const server = http.createServer(app);
const io = new socketIo.Server(server, { cors: { origin: "*" } });

app.use(
    cors({
        exposedHeaders: ["RateLimit-Reset", "Content-Disposition", "Content-Type"],
        origin: true,
        credentials: true,
    }),
);

app.use(cookieParser());
app.use(express.json({ limit: "20mb" }));

app.use("/api", (req, res, next) => {
    console.log(`[${req.method}: ${req.url}]`);
    if (!req.cookies.deviceId) {
        const deviceId = uuidv4();
        res.cookie("deviceId", deviceId, {
            expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30),
        });
        console.log("New deviceId generated and set in cookie:", deviceId);
    } else {
        console.log("Existing deviceId from cookie:", req.cookies.deviceId);
    }
    next();
});
app.use("/api/docs", swaggerUi.serve, swaggerUi.setup(swaggerDocs, swaggerConfig));
app.use("/api", require("./routes/index")); // this is a v1 version route
app.use("/api/v2", require("./routes/v2/index.v2")); // this is a v2 version route

// TODO: fix routing
// app.use('/api/docs', swaggerUi.serve, swaggerUi.setup(swaggerDocs, swaggerConfig));
// app.use('/api/users', userRouter);
// app.use('/api/kinesis', kinesisRouter);
// app.use('/api/regions', regionRouter);
// app.use('/api/vesselLocations', vesselLocationRouter);
// app.use('/api/roles', roleRouter);
// app.use('/api/permissions', permissionRouter);
// app.use('/api/artifacts', artifactRouter);
// app.use('/api/logs', logRouter);
// app.use('/api/s3', s3Router);
// app.use('/api/apiKeys', apiKeyRouter);
// app.use('/api/apiEndpoints', apiEndpointRouter);
// app.use('/api/statistics', statisticsRouter);
// app.use('/api/vessels', vesselRouter);
// app.use('/api/geolocations', geolocationRouter);
// app.use('/api/tourGuides', tourGuideRouter);
// app.use('/api/notificationsAlerts', notificationAlertRouter);
// app.use('/api/inAppNotifications', inAppNotificationRouter);

app.use("/api/*", (req, res) => res.status(404).send(`<h3>Sorry, that route does not exist.</h3>`));
app.use("/api", (req, res) => res.send(`<h3>Welcome to Quartermaster API</h3>`));

app.use(express.static(path.join(__dirname, "frontend", "dist")));

app.get("*", (req, res) => {
    res.sendFile(path.join(__dirname, "frontend", "dist", "index.html"));
});

const PORT = Number(process.env.PORT);

// mongoose.connect(process.env.MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true })
//     .then(() => {
//         server.listen(PORT, () => {
//             console.log(`Server running on port ${PORT}`);
//         });
//     })
//     .catch(err => console.log(err));

if (process.env.NODE_ENV !== "test") {
    const rServer = server.listen(PORT, () => {
        console.log(`Server running at http://localhost:${PORT}`);
    });

    rServer.setTimeout(600000);
}

io.use((socket, next) => {
    try {
        const { jwt_token } = socket.handshake.auth;
        if (!jwt_token) return next(new Error("Authentication error: No token provided"));

        const { user_id } = jwt.verify(jwt_token, process.env.JWT_SECRET as string) as JwtPayload;
        if (!user_id) return next(new Error("Authentication error: Invalid token"));

        socket.handshake.auth.user_id = user_id;

        next();
    } catch (err: any) {
        next(new Error(err.message));
    }
});

io.on("connection", async (socket) => {
    console.log(`User connected ${socket.id}. Total connections: ${io.sockets.sockets.size}`);
    socket.on("disconnect", async () => {
        console.log(`User disconnected ${socket.id}. Total connections: ${io.sockets.sockets.size}`);

        await SessionLog.updateOne({ socket_id: socket.id }, { $set: { disconnect_timestamp: new Date().toISOString() } });
    });

    SessionLog.create({
        socket_id: socket.id,
        device: socket.handshake.auth.device,
        browser: socket.handshake.auth.browser,
        user_id: new mongoose.Types.ObjectId(socket.handshake.auth.user_id),
    });
});

ioEmitter.on("notifyAll", (event) => {
    io.emit(event.name, event.data);
});

process.on("uncaughtException", (err) => {
    console.error("(FATAL ERROR) Uncaught Exception:", err);
    console.error(err);
});

//TODO: verify redis export
export default app;
export { server, io, redisClient };
