import { describe, it, beforeEach, afterEach, expect, jest } from '@jest/globals';
import request from 'supertest';

jest.mock('../modules/db', () => ({
    qm: { model: jest.fn(() => ({})) },
    connect: jest.fn()
}));

jest.mock('../modules/processLogs', () => ({}));

jest.mock('../routes/index', () => {
    const express = require('express');
    const router = express.Router();
    router.get('/', (_req: any, res: any) => res.send('Welcome to Quartermaster API'));
    return router;
});

jest.mock('../routes/v2/index.v2', () => {
    const express = require('express');
    const router = express.Router();
    router.get('/', (_req: any, res: any) => res.send('Welcome to Quartermaster API v2'));
    return router;
});

jest.mock('../modules/swagger', () => {
    const swaggerUi = {
        serve: (_req: any, _res: any, next: any) => next(),
        setup: () => (_req: any, res: any) => res.status(200).send('swagger'),
    };
    return {
        swaggerUi,
        swaggerDocs: {},
        swaggerConfig: {},
    };
});

const emitSpy = jest.fn();
let savedAuthMw: any;
let savedConnectionHandler: any;

jest.mock('socket.io', () => {
    class FakeServer {
        constructor() { }
        use(fn: any) { savedAuthMw = fn; }
        on(event: string, handler: any) { if (event === 'connection') savedConnectionHandler = handler; }
        emit = emitSpy;
        get sockets() { return { sockets: new Map() }; }
    }
    return { Server: FakeServer };
});

jest.mock('../models/SessionLog', () => ({
    updateOne: jest.fn().mockResolvedValue(true as never),
    create: jest.fn().mockResolvedValue(true as never),
}));

const verifyMock = jest.fn();
jest.mock('jsonwebtoken', () => ({
    __esModule: true,
    default: { verify: (...args: any[]) => (verifyMock as any)(...args) },
}));

const onSpy = jest.fn();
let disconnectHandler: any;

jest.mock('../modules/ioEmitter', () => {
    const { EventEmitter } = require('events');
    const emitter = new EventEmitter();
    return { __esModule: true, default: emitter };
});

const ORIGINAL_ENV = process.env;

describe('server.ts', () => {
    beforeEach(() => {
        jest.resetModules();
        jest.clearAllMocks();
        process.env = { ...ORIGINAL_ENV, NODE_ENV: 'test', PORT: '3333', JWT_SECRET: 'secret' };
        savedAuthMw = undefined;
        savedConnectionHandler = undefined;
    });

    afterEach(() => {
        process.env = ORIGINAL_ENV;
    });

    it('sets deviceId cookie and serves welcome/404 routes', async () => {
        const mod = await import('../server');
        const app = mod.default;

        const resWelcome = await request(app).get('/api');
        expect(resWelcome.status).toBe(200);
        expect(resWelcome.headers['set-cookie']).toBeDefined();

        const res404 = await request(app).get('/api/unknown/path');
        expect(res404.status).toBe(404);
    });

    it('serves swagger docs', async () => {
        const mod = await import('../server');
        const app = mod.default;
        const res = await request(app).get('/api/docs');
        expect(res.status).toBe(200);
    });

    it('socket.io auth middleware accepts valid token and handles connection/disconnect', async () => {
        await import('../server');
        const SessionLog = (await import('../models/SessionLog')).default || (await import('../models/SessionLog'));

        verifyMock.mockReturnValue({ user_id: '507f1f77bcf86cd799439011' });

        const socket: any = {
            id: 'sock-1',
            handshake: { auth: { jwt_token: 'token', device: 'dev', browser: 'br' } },
            on: (event: string, handler: any) => { if (event === 'disconnect') disconnectHandler = handler; onSpy(event); },
        };

        const next = jest.fn();
        await savedAuthMw(socket, next);
        expect(next).toHaveBeenCalled();
        expect(socket.handshake.auth.user_id).toBe('507f1f77bcf86cd799439011');

        await savedConnectionHandler(socket);
        expect(SessionLog.create).toHaveBeenCalled();

        await disconnectHandler?.();
        expect(SessionLog.updateOne).toHaveBeenCalledWith({ socket_id: 'sock-1' }, expect.any(Object));
    });

    it('socket.io auth middleware rejects missing token', async () => {
        await import('../server');
        const socket: any = { handshake: { auth: {} } };
        const next = jest.fn();
        await savedAuthMw(socket, next);
        expect(next).toHaveBeenCalledWith(expect.any(Error));
        expect((next.mock.calls[0][0] as Error).message).toMatch('No token provided');
    });

    it('socket.io auth middleware propagates jwt errors', async () => {
        await import('../server');
        verifyMock.mockImplementation(() => { throw new Error('bad'); });
        const socket: any = { handshake: { auth: { jwt_token: 'x' } } };
        const next = jest.fn();
        await savedAuthMw(socket, next);
        expect(next).toHaveBeenCalledWith(expect.any(Error));
        expect((next.mock.calls[0][0] as Error).message).toBe('bad');
    });

    it('ioEmitter notifyAll emits to all sockets', async () => {
        await import('../server');
        const ioEmitterMod: any = await import('../modules/ioEmitter');
        ioEmitterMod.default.emit('notifyAll', { name: 'evt', data: { a: 1 } });
        expect(emitSpy).toHaveBeenCalledWith('evt', { a: 1 });
    });

    it('serves frontend catch-all route', async () => {
        const mod = await import('../server');
        const app = mod.default;
        const res = await request(app).get('/some-random-path');
        expect([200, 304]).toContain(res.status);
    });

    it('logs API requests without authorization', async () => {
        const consoleSpy = jest.spyOn(console, 'log').mockImplementation((...args: any[]) => args[0]);
        const mod = await import('../server');
        const app = mod.default;

        await request(app).get('/api/test');
        expect(consoleSpy).toHaveBeenCalledWith(
            expect.stringContaining('[GET: /api/test] user: public')
        );

        consoleSpy.mockRestore();
    });

    it('sets deviceId cookie when not present', async () => {
        const mod = await import('../server');
        const app = mod.default;

        const res = await request(app).get('/api/test');
        expect(res.headers['set-cookie']).toBeDefined();
        expect(res.headers['set-cookie']?.[0]).toContain('deviceId=');
    });

    it('does not set deviceId cookie when already present', async () => {
        const mod = await import('../server');
        const app = mod.default;

        const res = await request(app)
            .get('/api/test')
            .set('Cookie', 'deviceId=existing-device-id');

        const setCookieHeader = res.headers['set-cookie'];
        if (setCookieHeader) {
            expect(setCookieHeader.includes('deviceId=')).toBe(false);
        }
    });

    it('covers frontend catch-all route with static file serving', async () => {
        const mod = await import('../server');
        const app = mod.default;

        const res = await request(app).get('/non-api-route');
        expect([200, 304]).toContain(res.status);
    });

    it('covers uncaught exception handler', async () => {
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation((...args: any[]) => args[0]);
        const processSpy = jest.spyOn(process, 'on').mockImplementation((...args: any[]) => args[0]);

        await import('../server');

        const uncaughtHandler = processSpy.mock.calls.find(call => call[0] === 'uncaughtException')?.[1];
        expect(uncaughtHandler).toBeDefined();

        if (uncaughtHandler) {
            const testError = new Error('test uncaught exception');
            uncaughtHandler(testError);
            expect(consoleSpy).toHaveBeenCalledWith('(FATAL ERROR) Uncaught Exception:', testError);
        }

        consoleSpy.mockRestore();
        processSpy.mockRestore();
    });

});