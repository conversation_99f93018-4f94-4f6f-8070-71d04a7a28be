import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('Statistics Model', () => {
    let mockMongoose: any;
    let mockDb: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create Statistics model with proper schema and hooks', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/Statistics')];

        const StatisticsModule = await import('../../models/Statistics');
        const Statistics = StatisticsModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('Statistics', expect.any(Object));
        expect(Statistics).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.stats).toBeDefined();
        expect(schemaArg.paths.stats.type).toBeDefined();
        expect(schemaArg.paths.stats.required).toBe(true);

        expect(schemaArg.paths.fromTimestamp).toBeDefined();
        expect(schemaArg.paths.fromTimestamp.type).toBe(Date);
        expect(schemaArg.paths.fromTimestamp.required).toBe(true);
        expect(schemaArg.paths.fromTimestamp.unique).toBe(true);

        expect(schemaArg.paths.toTimestamp).toBeDefined();
        expect(schemaArg.paths.toTimestamp.type).toBe(Date);
        expect(schemaArg.paths.toTimestamp.required).toBe(true);
        expect(schemaArg.paths.toTimestamp.unique).toBe(true);

        expect(schemaArg.paths.type).toBeDefined();
        expect(schemaArg.paths.type.type).toBe(String);
        expect(schemaArg.paths.type.required).toBe(true);
        expect(schemaArg.paths.type.enum).toEqual(["weekly", "daily"]);
    });
});
