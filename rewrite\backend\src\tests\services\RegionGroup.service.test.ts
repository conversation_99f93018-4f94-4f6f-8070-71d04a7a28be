import { describe, it, beforeEach, expect, jest } from '@jest/globals';
import regionGroupService from '../../services/RegionGroup.service';
import RegionGroup from '../../models/RegionGroup';
import vesselService from '../../services/Vessel.service';
import streamService from '../../services/Stream.service';

jest.mock('../../models/RegionGroup', () => require('../mocks/models/regionGroup.mock'));
jest.mock('../../services/Vessel.service', () => require('../mocks/services/vesselService.mock'));
jest.mock('../../services/Stream.service', () => ({ __esModule: true, default: { resetCache: jest.fn() } }));

describe('RegionGroupService', () => {
    beforeEach(() => {
        jest.resetAllMocks();
    });

    it('find builds pipeline, looks up creator, and maps vessels', async () => {
        (RegionGroup.aggregate as jest.Mock).mockResolvedValue([{ _id: 'RG1', name: 'A', timezone: 'UTC', created_by: { _id: 'U', name: 'N' } }] as never);
        (vesselService.find as jest.Mock).mockResolvedValue([{ _id: 'V1', name: 'V', unit_id: 'U1', region_group_id: 'RG1' }] as never);
        await regionGroupService.find({ name: 'A' } as any, { name: 1 } as any);
        expect(RegionGroup.aggregate).toHaveBeenCalled();
    });

    it('findById validates id and returns null when missing', async () => {
        (RegionGroup.aggregate as jest.Mock).mockResolvedValue([] as never);
        const res = await regionGroupService.findById({ id: '6529fd5e6a0c1f23b8b0d3a1' });
        expect(res).toBeNull();
    });

    it('findById returns with vessels mapped', async () => {
        (RegionGroup.aggregate as jest.Mock).mockResolvedValue([{ _id: 'RG1', name: 'A', timezone: 'UTC', created_by: { _id: 'U', name: 'N' } }] as never);
        (vesselService.find as jest.Mock).mockResolvedValue([{ _id: 'V1', name: 'V', unit_id: 'U1', region_group_id: 'RG1' }] as never);
        const res = await regionGroupService.findById({ id: '6529fd5e6a0c1f23b8b0d3a1' });
        expect(res?.vessels?.[0]).toEqual({ vessel_id: 'V1', unit_id: 'U1', name: 'V' });
    });

    it('findById throws on invalid id', async () => {
        await expect(regionGroupService.findById({ id: 'bad' as any })).rejects.toThrow('Invalid region group id');
    });

    it('create resets cache and refetches by id', async () => {
        (RegionGroup.create as jest.Mock).mockResolvedValue({ _id: '6529fd5e6a0c1f23b8b0d3a1' } as never);
        (RegionGroup.aggregate as jest.Mock).mockResolvedValue([{ _id: '6529fd5e6a0c1f23b8b0d3a1', name: 'A', timezone: 'UTC', created_by: { _id: 'U', name: 'N' } }] as never);
        (vesselService.find as jest.Mock).mockResolvedValue([] as never);
        const res = await regionGroupService.create({ name: 'A', timezone: 'UTC', created_by: '6529fd5e6a0c1f23b8b0d3a1' as any });
        expect(streamService.resetCache).toHaveBeenCalled();
        expect(res?._id).toBe('6529fd5e6a0c1f23b8b0d3a1');
    });

    it('update validates id, cleans data, resets cache, returns refetched', async () => {
        (RegionGroup.findByIdAndUpdate as jest.Mock).mockResolvedValue({ _id: '6529fd5e6a0c1f23b8b0d3a1' } as never);
        (RegionGroup.aggregate as jest.Mock).mockResolvedValue([{ _id: 'RG1', name: 'A', timezone: 'UTC', created_by: { _id: 'U', name: 'N' } }] as never);
        (vesselService.find as jest.Mock).mockResolvedValue([] as never);
        const res = await regionGroupService.update({ id: '6529fd5e6a0c1f23b8b0d3a1', name: undefined, timezone: 'UTC' });
        expect(streamService.resetCache).toHaveBeenCalled();
        expect(res?._id).toBe('RG1');
    });

    it('update throws on invalid id', async () => {
        await expect(regionGroupService.update({ id: 'bad', timezone: 'UTC' })).rejects.toThrow('Invalid region group id');
    });

    it('delete throws 409 when vessels exist', async () => {
        (vesselService.find as jest.Mock).mockResolvedValue([{ _id: 'V1' }] as never);
        await expect(regionGroupService.delete({ id: 'RG1' })).rejects.toMatchObject({ status: 409 });
    });

    it('delete removes group and resets cache', async () => {
        (vesselService.find as jest.Mock).mockResolvedValue([] as never);
        (RegionGroup.findByIdAndDelete as jest.Mock).mockResolvedValue({ _id: 'RG1' } as never);
        const res = await regionGroupService.delete({ id: 'RG1' });
        expect(res).toBe(true);
        expect(streamService.resetCache).toHaveBeenCalled();
    });

    it('delete returns false when group not found', async () => {
        (vesselService.find as jest.Mock).mockResolvedValue([] as never);
        (RegionGroup.findByIdAndDelete as jest.Mock).mockResolvedValue(null as never);
        const res = await regionGroupService.delete({ id: 'RGX' });
        expect(res).toBe(false);
        expect(streamService.resetCache).toHaveBeenCalled();
    });

    it('update falls back to provided id when update returns null', async () => {
        (RegionGroup.findByIdAndUpdate as jest.Mock).mockResolvedValue(null as never);
        (RegionGroup.aggregate as jest.Mock).mockResolvedValue([{ _id: '6529fd5e6a0c1f23b8b0d3a1', name: 'A', timezone: 'UTC', created_by: { _id: 'U', name: 'N' } }] as never);
        (vesselService.find as jest.Mock).mockResolvedValue([] as never);
        const res = await regionGroupService.update({ id: '6529fd5e6a0c1f23b8b0d3a1', name: 'A' });
        expect(streamService.resetCache).toHaveBeenCalled();
        expect(res?._id).toBe('6529fd5e6a0c1f23b8b0d3a1');
    });
});


