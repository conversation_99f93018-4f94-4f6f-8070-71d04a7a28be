import express, { Request, Response } from "express";
import assignEndpointId from "../../middlewares/assignEndpointId";
import { endpointIds } from "../../utils/endpointIds";
import { validateData } from "../../middlewares/validator";
import { query } from "express-validator";
import jwt from "jsonwebtoken";
import { Readable } from "node:stream";
import { ClusterFeature, getStaticMap } from "../../utils/staticMap";
import type { ReadableStream } from "stream/web";

const router = express.Router();

router.get(
    "/map.:ext?",
    assignEndpointId.bind(this, endpointIds.FETCH_MAP_FOR_SUMMARIES_V2),
    validateData.bind(this, [
        query("token")
            .isString()
            .notEmpty()
            .withMessage((value: string, { path }: { path: string }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req: Request, res: Response) => {
        // decrypt jwt_token to get locations
        const { token } = req.query as { token: string };
        const { markers } = jwt.verify(token, process.env.JWT_SECRET as string) as { markers: ClusterFeature[] };

        try {
            if (!markers) {
                return res.status(500).send({ message: "Error processing request" });
            }

            const { ext } = req.params;

            const mapData = await getStaticMap(markers, ext);

            if (mapData && "mimeType" in mapData && "source" in mapData) {
                res.setHeader("Content-Type", mapData.mimeType);
                if (mapData.source && mapData.source.headers && mapData.source.headers.get("content-length")) {
                    res.setHeader("Content-Length", mapData.source.headers.get("content-length") as string);
                }
                res.setHeader("access-control-allow-origin", "*");
                res.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
                res.setHeader("Expires", "0");
                res.setHeader("Pragma", "no-cache");

                if (mapData.source) {
                    const nodeStream = Readable.fromWeb(mapData.source.body as ReadableStream);
                    nodeStream.pipe(res);
                } else {
                    return res.status(500).send({ message: "Error serving image" });
                }
            } else {
                res.status(500).send({ message: "Error serving image" });
            }
        } catch (error) {
            console.error("Error serving image:", error);
            res.status(500).send({ message: "Error serving image" });
        }
    },
);

export default router;
