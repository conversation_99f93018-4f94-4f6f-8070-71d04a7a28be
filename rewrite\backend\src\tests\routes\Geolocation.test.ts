import request from 'supertest';
import app from '../../server';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } from '../data/Auth';
import User from '../../models/User';
import ApiKey from '../../models/ApiKey';
import Geolocation from '../../models/Geolocation';
import { fetchGeolocation } from '../../modules/geolocation';
import { setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../models/Geolocation', () => require('../mocks/models/geolocation.mock'));
jest.mock('../../modules/geolocation', () => require('../mocks/modules/geolocation.mock'));

describe('Geolocation API', () => {
    describe('GET /api/geolocations', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/geolocations');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if lat is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/geolocations')
                        .query({ lng: '37.7749' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if lng is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/geolocations')
                        .query({ lat: '37.7749' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if lat is not a float', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/geolocations')
                        .query({ lat: 'invalid', lng: '37.7749' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if lng is not a float', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/geolocations')
                        .query({ lat: '37.7749', lng: 'invalid' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 200 with existing geolocation', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Geolocation.findOne as any).mockResolvedValueOnce({
                        name: 'San Francisco, CA, USA'
                    });

                    const res = await request(app)
                        .get('/api/geolocations')
                        .query({ lat: '37.7749', lng: '-122.4194' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('lat', '37.7749');
                    expect(res.body).toHaveProperty('lng', '-122.4194');
                    expect(res.body).toHaveProperty('name', 'San Francisco, CA, USA');
                });

                it('should return 200 with new geolocation from API', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Geolocation.findOne as any).mockResolvedValueOnce(null);
                    (fetchGeolocation as any).mockResolvedValueOnce('San Francisco, CA, USA');
                    (Geolocation.create as any).mockResolvedValueOnce({});

                    const res = await request(app)
                        .get('/api/geolocations')
                        .query({ lat: '37.7749', lng: '-122.4194' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('lat', '37.7749');
                    expect(res.body).toHaveProperty('lng', '-122.4194');
                    expect(res.body).toHaveProperty('name', 'San Francisco, CA, USA');
                });

                it('should return 200 with Unknown Location when API fails', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Geolocation.findOne as any).mockResolvedValueOnce(null);
                    (fetchGeolocation as any).mockRejectedValueOnce(new Error('API Error'));
                    (Geolocation.create as any).mockResolvedValueOnce({});

                    const res = await request(app)
                        .get('/api/geolocations')
                        .query({ lat: '37.7749', lng: '-122.4194' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('lat', '37.7749');
                    expect(res.body).toHaveProperty('lng', '-122.4194');
                    expect(res.body).toHaveProperty('name', 'Unknown Location');
                });

                it('should return 200 with Unknown Location when API returns null', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Geolocation.findOne as any).mockResolvedValueOnce(null);
                    (fetchGeolocation as any).mockResolvedValueOnce(null);
                    (Geolocation.create as any).mockResolvedValueOnce({});

                    const res = await request(app)
                        .get('/api/geolocations')
                        .query({ lat: '37.7749', lng: '-122.4194' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('lat', '37.7749');
                    expect(res.body).toHaveProperty('lng', '-122.4194');
                    expect(res.body).toHaveProperty('name', 'Unknown Location');
                });

                it('should return 500 on database error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Geolocation.findOne as any).mockRejectedValueOnce(new Error('Database error'));

                    const res = await request(app)
                        .get('/api/geolocations')
                        .query({ lat: '37.7749', lng: '-122.4194' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                    expect(res.body).toHaveProperty('message', 'Error retrieving geolocation');
                    expect(res.body).toHaveProperty('error', 'Database error');
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});