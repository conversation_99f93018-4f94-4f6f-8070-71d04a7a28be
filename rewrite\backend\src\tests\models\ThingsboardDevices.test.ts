import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('ThingsboardDevices Model', () => {
    let mockMongoose: any;
    let mockDb: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qmShared');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create ThingsboardDevices model with proper schema and hooks', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/ThingsboardDevices')];

        const ThingsboardDevicesModule = await import('../../models/ThingsboardDevices');
        const ThingsboardDevices = ThingsboardDevicesModule.default;

        expect(mockDb.qmShared.model).toHaveBeenCalledWith('ThingsboardDevices', expect.any(Object), 'thingsboard_devices');
        expect(ThingsboardDevices).toBeDefined();

        const schemaArg = mockDb.qmShared.model.mock.calls[0][1];

        expect(schemaArg.paths.deviceId).toBeDefined();
        expect(schemaArg.paths.deviceId.type).toBe(String);
        expect(schemaArg.paths.deviceId.required).toBe(true);
        expect(schemaArg.paths.deviceId.unique).toBe(true);

        expect(schemaArg.paths.dashboardId).toBeDefined();
        expect(schemaArg.paths.dashboardId.type).toBe(String);
        expect(schemaArg.paths.dashboardId.required).toBe(false);

        expect(schemaArg.paths.deviceName).toBeDefined();
        expect(schemaArg.paths.deviceName.type).toBe(String);
        expect(schemaArg.paths.deviceName.required).toBe(true);

        expect(schemaArg.paths.accessToken).toBeDefined();
        expect(schemaArg.paths.accessToken.type).toBe(String);
        expect(schemaArg.paths.accessToken.required).toBe(true);

        expect(schemaArg.index).toHaveBeenCalledWith(
            { dashboardId: 1 },
            {
                unique: true,
                partialFilterExpression: { dashboardId: { $type: "string" } },
            }
        );
    });
});
