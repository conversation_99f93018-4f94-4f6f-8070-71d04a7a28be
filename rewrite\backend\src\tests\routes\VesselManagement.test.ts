import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import request from 'supertest';
import app from '../../server';
import User from '../../models/User';
import ApiKey from '../../models/ApiKey';
import { generateUserToken, generateApiToken, authorizedUser, nonAuthorizedUser, authorizedApiKey, nonAuthorizedApiKey } from '../data/Auth';
import { setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';
import vesselService from '../../services/Vessel.service';
import streamService from '../../services/Stream.service';
import { upload, handleMulterError } from '../mocks/middlewares/multerConfig.mock';
import restrictEndpointByUser from '../mocks/middlewares/restrictEndpointByUser.mock';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'portal';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../services/Vessel.service', () => require('../mocks/services/vessel.mock'));
jest.mock('../../services/Stream.service', () => require('../mocks/services/stream.mock'));
jest.mock('../../middlewares/multerConfig', () => require('../mocks/middlewares/multerConfig.mock'));
jest.mock('../../middlewares/restrictEndpointByUser', () => require('../mocks/middlewares/restrictEndpointByUser.mock'));
jest.mock('../../middlewares/validator', () => require('../mocks/middlewares/validator.mock'));
jest.mock('../../utils/userEndpointRestrictions', () => ({
    userEndpointRestrictions: {
        2303: ['66e2e452bca74bbdb726369c'],
        2304: ['66e2e452bca74bbdb726369c'],
    },
}));

const mockVesselService = vesselService as jest.Mocked<typeof vesselService>;
const mockStreamService = streamService as jest.Mocked<typeof streamService>;

const mockVessels = [
    {
        _id: '507f1f77bcf86cd799439012',
        name: 'Test Vessel 1',
        unit_id: 'unit1',
        is_active: true,
        region_group_id: '66e2e452bca74bbdb726369c',
        created_by: '507f1f77bcf86cd799439011',
        home_port_location: [10.0, 20.0],
    },
    {
        _id: '507f1f77bcf86cd799439013',
        name: 'Test Vessel 2',
        unit_id: 'unit2',
        is_active: false,
        region_group_id: '66e2e452bca74bbdb726369c',
        created_by: '507f1f77bcf86cd799439011',
        home_port_location: [11.0, 21.0],
    },
];

const mockStreams = [
    {
        unit_id: 'unit1',
        name: 'Test Stream 1',
        region: 'region1',
    },
    {
        unit_id: 'unit2',
        name: 'Test Stream 2',
        region: 'region2',
    },
];

const mockPaginatedResult = {
    vessels: mockVessels,
    totalCount: 2,
    totalPages: 1,
    currentPage: 1,
    hasNextPage: false,
    hasPrevPage: false,
};

describe('VesselManagement API', () => {
    beforeEach(() => {
        jest.resetAllMocks();
    });

    describe('GET /api/vesselManagement', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            describe(`${authMethod} authentication`, () => {
                const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 200 and paginated vessels for authorized user', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    mockVesselService.fetchPaginated.mockResolvedValueOnce(mockPaginatedResult as any);

                    const res = await request(app)
                        .get('/api/vesselManagement')
                        .query({ search: 'test' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('vessels');
                    expect(res.body).toHaveProperty('totalCount');
                    expect(res.body.vessels).toHaveLength(2);
                });

                it('should return 400 for invalid page parameter', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/vesselManagement')
                        .query({ page: 0, limit: 10 })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid limit parameter', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/vesselManagement')
                        .query({ page: 1, limit: 101 })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app)
                        .get('/api/vesselManagement')
                        .query({ page: 1, limit: 10 });

                    expect(res.status).toBe(401);
                });

                it('should return 500 on service error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    mockVesselService.fetchPaginated.mockRejectedValueOnce(new Error('Service error'));

                    const res = await request(app)
                        .get('/api/vesselManagement')
                        .query({ page: 1, limit: 10 })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/vesselManagement/unitIds', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            describe(`${authMethod} authentication`, () => {
                const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 200 and unit details for authorized user', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    mockStreamService.fetchAll.mockResolvedValueOnce(mockStreams as any);

                    const res = await request(app)
                        .get('/api/vesselManagement/unitIds')
                        .query({ regions: 'region1,region2' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveLength(2);
                    expect(res.body[0]).toHaveProperty('unit_id');
                    expect(res.body[0]).toHaveProperty('name');
                    expect(res.body[0]).toHaveProperty('region');
                });

                it('should return 200 without regions filter', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    mockStreamService.fetchAll.mockResolvedValueOnce(mockStreams as any);

                    const res = await request(app)
                        .get('/api/vesselManagement/unitIds')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveLength(2);
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app)
                        .get('/api/vesselManagement/unitIds');

                    expect(res.status).toBe(401);
                });

                it('should return 500 on service error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    mockStreamService.fetchAll.mockRejectedValueOnce(new Error('Service error'));

                    const res = await request(app)
                        .get('/api/vesselManagement/unitIds')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/vesselManagement/assignedUnitIds', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            describe(`${authMethod} authentication`, () => {
                const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 200 and assigned unit IDs for authorized user', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    mockVesselService.getAllAssignedUnitIds.mockResolvedValueOnce(['unit1', 'unit2'] as any);

                    const res = await request(app)
                        .get('/api/vesselManagement/assignedUnitIds')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(['unit1', 'unit2']);
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app)
                        .get('/api/vesselManagement/assignedUnitIds');

                    expect(res.status).toBe(401);
                });

                it('should return 500 on service error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    mockVesselService.getAllAssignedUnitIds.mockRejectedValueOnce(new Error('Service error'));

                    const res = await request(app)
                        .get('/api/vesselManagement/assignedUnitIds')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/vesselManagement/all', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            describe(`${authMethod} authentication`, () => {
                const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 200 and all vessels for authorized user', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    mockVesselService.find.mockResolvedValueOnce(mockVessels as any);

                    const res = await request(app)
                        .get('/api/vesselManagement/all')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveLength(2);
                    expect(res.body[0]).toHaveProperty('_id');
                    expect(res.body[0]).toHaveProperty('name');
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app)
                        .get('/api/vesselManagement/all');

                    expect(res.status).toBe(401);
                });

                it('should return 500 on service error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    mockVesselService.find.mockRejectedValueOnce(new Error('Service error'));

                    const res = await request(app)
                        .get('/api/vesselManagement/all')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/vesselManagement/:id', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            describe(`${authMethod} authentication`, () => {
                const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 200 and vessel by ID for authorized user', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    mockVesselService.findById.mockResolvedValueOnce(mockVessels[0] as any);

                    const res = await request(app)
                        .get('/api/vesselManagement/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('_id', '507f1f77bcf86cd799439012');
                    expect(res.body).toHaveProperty('name');
                });

                it('should return 404 when vessel not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    mockVesselService.findById.mockResolvedValueOnce(null as any);

                    const res = await request(app)
                        .get('/api/vesselManagement/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(404);
                    expect(res.body).toHaveProperty('message', 'Vessel not found');
                });

                it('should return 400 for invalid vessel ID', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/vesselManagement/invalid-id')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app)
                        .get('/api/vesselManagement/507f1f77bcf86cd799439012');

                    expect(res.status).toBe(401);
                });

                it('should return 500 on service error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    mockVesselService.findById.mockRejectedValueOnce(new Error('Service error'));

                    const res = await request(app)
                        .get('/api/vesselManagement/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/vesselManagement', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            describe(`${authMethod} authentication`, () => {
                const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

                beforeEach(() => {
                    jest.resetAllMocks();
                    upload.single.mockImplementation((_fieldName: any) => {
                        return (req: any, _res: any, next: any) => {
                            req.file = undefined;
                            next();
                        };
                    });
                    handleMulterError.mockImplementation((_req: any, _res: any, next: any) => {
                        next();
                    });
                    restrictEndpointByUser.mockImplementation((_req: any, _res: any, next: any) => {
                        next();
                    });
                });

                it('should return 201 and create vessel for authorized user', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    mockVesselService.create.mockResolvedValueOnce(mockVessels[0] as any);

                    const res = await request(app)
                        .post('/api/vesselManagement')
                        .set('Authorization', authToken)
                        .send({
                            name: 'Test Vessel',
                            region_group_id: '66e2e452bca74bbdb726369c',
                            home_port_location: JSON.stringify([10.0, 20.0])
                        });

                    expect(res.status).toBe(201);
                });

                it('should return 400 for missing required home_port_location field', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/vesselManagement')
                        .set('Authorization', authToken)
                        .send({
                            home_port_location: null
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid home_port_location field', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    jest.doMock('../../utils/functions', () => require('../mocks/utils/functions.mock'));
                    const res = await request(app)
                        .post('/api/vesselManagement')
                        .set('Authorization', authToken)
                        .send({
                            home_port_location: 'invalid-home-port-location'
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for missing required name field', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/vesselManagement')
                        .set('Authorization', authToken)
                        .send({
                            region_group_id: '66e2e452bca74bbdb726369c'
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for missing required region_group_id field', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/vesselManagement')
                        .set('Authorization', authToken)
                        .send({
                            name: 'Test Vessel'
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid region_group_id format', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/vesselManagement')
                        .set('Authorization', authToken)
                        .send({
                            name: 'Test Vessel',
                            region_group_id: 'invalid-id'
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid home_port_location format', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/vesselManagement')
                        .set('Authorization', authToken)
                        .send({
                            name: 'Test Vessel',
                            region_group_id: '66e2e452bca74bbdb726369c',
                            home_port_location: [10.0]
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app)
                        .post('/api/vesselManagement')
                        .send({
                            name: 'Test Vessel',
                            region_group_id: '66e2e452bca74bbdb726369c'
                        });

                    expect(res.status).toBe(401);
                });

                it('should return 500 on service error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    mockVesselService.create.mockRejectedValueOnce(new Error('Service error'));

                    const res = await request(app)
                        .post('/api/vesselManagement')
                        .set('Authorization', authToken)
                        .send({
                            name: 'Test Vessel',
                            region_group_id: '66e2e452bca74bbdb726369c'
                        });

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('PUT /api/vesselManagement/:id', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            describe(`${authMethod} authentication`, () => {
                const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

                beforeEach(() => {
                    jest.resetAllMocks();
                    upload.single.mockImplementation((_fieldName: any) => {
                        return (req: any, _res: any, next: any) => {
                            req.file = undefined;
                            next();
                        };
                    });
                    handleMulterError.mockImplementation((_req: any, _res: any, next: any) => {
                        next();
                    });
                    restrictEndpointByUser.mockImplementation((_req: any, _res: any, next: any) => {
                        next();
                    });
                    jest.doMock('../../utils/functions', () => require('../mocks/utils/functions.mock'));
                });

                it('should return 200 and update vessel for authorized user', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    mockVesselService.update.mockResolvedValueOnce(mockVessels[0] as any);

                    const res = await request(app)
                        .put('/api/vesselManagement/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({
                            name: 'Updated Vessel',
                            is_active: true
                        });

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('_id');
                    expect(res.body).toHaveProperty('name');
                });

                it('should return 200 and update vessel with all fields', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    mockVesselService.update.mockResolvedValueOnce(mockVessels[0] as any);

                    const res = await request(app)
                        .put('/api/vesselManagement/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({
                            name: 'Updated Vessel',
                            unit_id: 'updated-unit-id',
                            is_active: false,
                            region_group_id: '66e2e452bca74bbdb726369c',
                            home_port_location: [15.0, 25.0]
                        });

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('_id');
                    expect(res.body).toHaveProperty('name');
                });

                it('should return 200 and update vessel with remove_thumbnail flag', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    mockVesselService.update.mockResolvedValueOnce(mockVessels[0] as any);

                    const res = await request(app)
                        .put('/api/vesselManagement/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({
                            name: 'Updated Vessel',
                            remove_thumbnail: true
                        });

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('_id');
                    expect(res.body).toHaveProperty('name');
                });

                it('should return 200 and update vessel with null home_port_location', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    mockVesselService.update.mockResolvedValueOnce(mockVessels[0] as any);

                    const res = await request(app)
                        .put('/api/vesselManagement/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({
                            name: 'Updated Vessel',
                            home_port_location: null
                        });

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('_id');
                    expect(res.body).toHaveProperty('name');
                });

                it('should return 400 for invalid vessel ID', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .put('/api/vesselManagement/invalid-id')
                        .set('Authorization', authToken)
                        .send({
                            name: 'Updated Vessel'
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for empty name when provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .put('/api/vesselManagement/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({
                            name: ''
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid region_group_id format', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .put('/api/vesselManagement/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({
                            region_group_id: 'invalid-id'
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid unit_id format', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .put('/api/vesselManagement/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({
                            unit_id: 123 // Should be string, not number
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid is_active format', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .put('/api/vesselManagement/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({
                            is_active: 'not-a-boolean'
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid remove_thumbnail format', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .put('/api/vesselManagement/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({
                            remove_thumbnail: 'not-a-boolean'
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid home_port_location format', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .put('/api/vesselManagement/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({
                            home_port_location: [10.0]
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 500 for invalid home_port_location JSON string (error not caught by validation)', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .put('/api/vesselManagement/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({
                            home_port_location: 'invalid-json-string'
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 500 for home_port_location that is not an array (error not caught by validation)', async () => {
                    try {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                        const res = await request(app)
                            .put('/api/vesselManagement/507f1f77bcf86cd799439012')
                            .set('Authorization', authToken)
                            .send({
                                home_port_location: 'not-an-array'
                            });

                        expect(res.status).toBe(400);
                    } catch (error) {
                        console.error(error);
                    }
                });

                it('should return 400 for home_port_location with wrong array length', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .put('/api/vesselManagement/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({
                            home_port_location: [10.0, 20.0, 30.0] // 3 elements instead of 2
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for home_port_location with invalid numbers', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .put('/api/vesselManagement/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({
                            home_port_location: [10.0, 'invalid-number']
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for home_port_location with NaN values', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .put('/api/vesselManagement/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({
                            home_port_location: [10.0, NaN]
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app)
                        .put('/api/vesselManagement/507f1f77bcf86cd799439012')
                        .send({
                            name: 'Updated Vessel'
                        });

                    expect(res.status).toBe(401);
                });

                it('should return 500 on service error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    mockVesselService.update.mockRejectedValueOnce(new Error('Service error'));

                    const res = await request(app)
                        .put('/api/vesselManagement/507f1f77bcf86cd799439012')
                        .set('Authorization', authToken)
                        .send({
                            name: 'Updated Vessel'
                        });

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

});