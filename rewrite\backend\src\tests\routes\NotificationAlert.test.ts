import User from '../../models/User';
import { generateUserToken, authorizedUser, nonAuthorizedUser } from '../data/Auth';
import ApiKey from '../../models/ApiKey';
import NotificationAlert from '../../models/NotificationAlert';
import request from "supertest";
import app from '../../server';
import { jest, describe, it, beforeEach, expect, afterEach } from '@jest/globals';
import { setupAuthMocks, setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';
import { buildStaticMarkerSignature, canAccessVessel, validateError, generateUnsubscribeToken, getStaticMapOld } from '../../utils/functions';
import vesselService from '../../services/Vessel.service';
import jwt from 'jsonwebtoken';
import { generateNumberedIconBuffer } from '../../utils/staticMap';
import microservice_socket from '../../modules/microservice_socket';
import EmailDomains from '../../models/EmailDomains';
import { sendEmail } from '../../modules/email';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';
process.env.API_URL = 'http://localhost:3000';
process.env.APP_URL = 'http://localhost:3001';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../models/NotificationAlert', () => require('../mocks/models/notificationAlert.mock'));
jest.mock('../../models/EmailDomains', () => require('../mocks/models/emailDomains.mock'));
jest.mock('../../services/Vessel.service', () => require('../mocks/services/vesselService.mock'));
jest.mock('../../modules/email', () => require('../mocks/modules/email.mock'));
jest.mock('../../modules/microservice_socket', () => require('../mocks/modules/microservice_socket.mock'));
jest.mock('../../utils/staticMap', () => require('../mocks/modules/staticMap.mock'));
jest.mock('../../utils/functions', () => require('../mocks/utils/functions.mock'));

describe('Notification Alert API', () => {

    describe('GET /api/notificationsAlerts', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/notificationsAlerts');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if the user does not have the required permissions', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);

                    const res = await request(app).get('/api/notificationsAlerts').set('Authorization', nonAuthToken);

                    expect(res.status).toBe(403);
                });

                it('should return 200 and fetch notification alerts if the user is authorized', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (NotificationAlert.countDocuments as jest.Mock).mockResolvedValue(5 as never);
                    (NotificationAlert.find as jest.Mock).mockReturnValue({
                        skip: jest.fn().mockReturnThis(),
                        limit: jest.fn().mockReturnThis(),
                        exec: jest.fn().mockResolvedValue([
                            {
                                _id: '507f1f77bcf86cd799439011',
                                super_category: ['Ship'],
                                sub_category: ['Battleship'],
                                country_flags: ['US'],
                                type: 'email',
                                title: ['Test Alert'],
                                vessel_ids: ['507f1f77bcf86cd799439012'],
                                receivers: ['<EMAIL>'],
                                created_at: new Date().toISOString(),
                                updated_at: new Date().toISOString(),
                                is_enabled: true,
                                created_by: '507f1f77bcf86cd799439013'
                            }
                        ] as never)
                    });

                    const res = await request(app).get('/api/notificationsAlerts').set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('data');
                    expect(res.body).toHaveProperty('total_pages');
                    expect(res.body).toHaveProperty('total_documents');
                    expect(res.body).toHaveProperty('current_page');
                    expect(res.body).toHaveProperty('next_page');
                    expect(res.body).toHaveProperty('previous_page');
                });

                it('should not return null on next_page and previous_page if there is only one page', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (NotificationAlert.countDocuments as jest.Mock).mockResolvedValue(100 as never);
                    (NotificationAlert.find as jest.Mock).mockReturnValue({
                        skip: jest.fn().mockReturnThis(),
                        limit: jest.fn().mockReturnThis(),
                        exec: jest.fn().mockResolvedValue([] as never)
                    });

                    const res = await request(app).get('/api/notificationsAlerts?page=2&page_size=10').set('Authorization', authToken);

                    expect(res.status).toBe(200);
                });

                it('should return 500 on internal error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (NotificationAlert.countDocuments as jest.Mock).mockRejectedValue(new Error('Database error') as never);

                    const res = await request(app).get('/api/notificationsAlerts').set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/notificationsAlerts', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                    (canAccessVessel as jest.Mock).mockResolvedValue(true as never);
                    (generateUnsubscribeToken as jest.Mock).mockReturnValue('mock-token');
                    (sendEmail as jest.Mock).mockResolvedValue(true as never);
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/notificationsAlerts');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if the user does not have the required permissions (validation runs first)', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);

                    const res = await request(app).post('/api/notificationsAlerts').set('Authorization', nonAuthToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if required fields are missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({});

                    expect(res.status).toBe(400);
                });

                it('should return 400 if super_category is empty', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: [],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'email',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012']
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 if sub_category is empty', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: [],
                            country_flags: ['US'],
                            type: 'email',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012']
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 if country_flags is empty (validation error)', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: [],
                            type: 'email',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012']
                        });

                    expect([400, 500]).toContain(res.status);
                });

                it('should return 400 if type is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'invalid',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012']
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 if title is empty', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'email',
                            title: [],
                            vessel_ids: ['507f1f77bcf86cd799439012']
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 if vessel_ids is empty', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'email',
                            title: ['Test Alert'],
                            vessel_ids: []
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 if vessel_ids contains invalid ObjectId', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'email',
                            title: ['Test Alert'],
                            vessel_ids: ['invalid-id']
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 if receivers contains invalid email', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'email',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012'],
                            receivers: ['invalid-email']
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 if is_enabled has invalid value', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'email',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012'],
                            is_enabled: 'invalid'
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 200 and create notification alert successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as jest.Mock).mockResolvedValue([
                        { _id: '507f1f77bcf86cd799439012', name: 'Test Vessel', assigned_unit_id: '507f1f77bcf86cd799439013' }
                    ] as never);
                    (NotificationAlert.create as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439011',
                        super_category: ['Ship'],
                        sub_category: ['Battleship'],
                        country_flags: ['US'],
                        type: 'email',
                        title: ['Test Alert'],
                        vessel_ids: ['507f1f77bcf86cd799439012'],
                        receivers: [],
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString(),
                        is_enabled: true,
                        created_by: '507f1f77bcf86cd799439013'
                    } as never);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'email',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012']
                        });

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('_id');
                    expect(res.body).toHaveProperty('super_category');
                    expect(res.body).toHaveProperty('sub_category');
                    expect(res.body).toHaveProperty('type');
                });

                it('should return 403 if user has no email for email type', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (User.findById as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439013',
                        email: null,
                        permissions: [{ permission_id: 'manageNotifications' }]
                    } as never);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'email',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012']
                        });

                    expect([403, 500]).toContain(res.status);
                });

                it('should return 403 if user tries to subscribe to unauthorized vessel', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as jest.Mock).mockResolvedValue([
                        { _id: '507f1f77bcf86cd799439012', name: 'Test Vessel' }
                    ] as never);
                    (canAccessVessel as jest.Mock).mockReturnValue(false);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'email',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012']
                        });

                    expect(res.status).toBe(403);
                });

                it('should return 500 on internal error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as jest.Mock).mockRejectedValue(new Error('Database error') as never);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'email',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012']
                        });

                    expect(res.status).toBe(500);
                });

                it('should return 400 if user email domain is not allowed', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as jest.Mock).mockResolvedValue([
                        { _id: '507f1f77bcf86cd799439012', name: 'Test Vessel' }
                    ] as never);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([
                        { domain: 'allowed.com' }
                    ] as never);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'email',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012'],
                            receivers: ['<EMAIL>']
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 if receiver email domain is not allowed', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as jest.Mock).mockResolvedValue([
                        { _id: '507f1f77bcf86cd799439012', name: 'Test Vessel' }
                    ] as never);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([
                        { domain: 'example.com' }
                    ] as never);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'email',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012'],
                            receivers: ['<EMAIL>']
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 400 if user has no email but tries to add receivers', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (User.findById as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439013',
                        email: null,
                        permissions: [{ permission_id: 'manageNotifications' }]
                    } as never);
                    (vesselService.find as jest.Mock).mockResolvedValue([
                        { _id: '507f1f77bcf86cd799439012', name: 'Test Vessel' }
                    ] as never);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'app',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012'],
                            receivers: ['<EMAIL>']
                        });

                    expect([400, 500]).toContain(res.status);
                });

                it('should create notification alert with app type successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as jest.Mock).mockResolvedValue([
                        { _id: '507f1f77bcf86cd799439012', name: 'Test Vessel' }
                    ] as never);
                    (NotificationAlert.create as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439011',
                        super_category: ['Ship'],
                        sub_category: ['Battleship'],
                        country_flags: ['US'],
                        type: 'app',
                        title: ['Test Alert'],
                        vessel_ids: ['507f1f77bcf86cd799439012'],
                        receivers: [],
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString(),
                        is_enabled: true,
                        created_by: '507f1f77bcf86cd799439013'
                    } as never);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'app',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012']
                        });

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('_id');
                    expect(res.body.type).toBe('app');
                });

                it('should create notification alert with both type successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as jest.Mock).mockResolvedValue([
                        { _id: '507f1f77bcf86cd799439012', name: 'Test Vessel' }
                    ] as never);
                    (NotificationAlert.create as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439011',
                        super_category: ['Ship'],
                        sub_category: ['Battleship'],
                        country_flags: ['US'],
                        type: 'both',
                        title: ['Test Alert'],
                        vessel_ids: ['507f1f77bcf86cd799439012'],
                        receivers: [],
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString(),
                        is_enabled: true,
                        created_by: '507f1f77bcf86cd799439013'
                    } as never);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'both',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012']
                        });

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('_id');
                    expect(res.body.type).toBe('both');
                });

                it('should create notification alert with receivers and send emails', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as jest.Mock).mockResolvedValue([
                        { _id: '507f1f77bcf86cd799439012', name: 'Test Vessel' }
                    ] as never);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([
                        { domain: 'example.com' }
                    ] as never);
                    (NotificationAlert.create as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439011',
                        super_category: ['Ship'],
                        sub_category: ['Battleship'],
                        country_flags: ['US'],
                        type: 'email',
                        title: ['Test Alert'],
                        vessel_ids: ['507f1f77bcf86cd799439012'],
                        receivers: ['<EMAIL>'],
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString(),
                        is_enabled: true,
                        created_by: '507f1f77bcf86cd799439013'
                    } as never);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'email',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012'],
                            receivers: ['<EMAIL>']
                        });

                    expect([200, 500]).toContain(res.status);
                    if (res.status === 200) {
                        expect(res.body).toHaveProperty('_id');
                    }
                });

                it('should create notification alert with both type and send emails', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as jest.Mock).mockResolvedValue([
                        { _id: '507f1f77bcf86cd799439012', name: 'Test Vessel' }
                    ] as never);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([
                        { domain: 'example.com' }
                    ] as never);
                    (NotificationAlert.create as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439011',
                        super_category: ['Ship'],
                        sub_category: ['Battleship'],
                        country_flags: ['US'],
                        type: 'both',
                        title: ['Test Alert'],
                        vessel_ids: ['507f1f77bcf86cd799439012'],
                        receivers: ['<EMAIL>'],
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString(),
                        is_enabled: true,
                        created_by: '507f1f77bcf86cd799439013'
                    } as never);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'both',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012'],
                            receivers: ['<EMAIL>']
                        });

                    expect([200, 500]).toContain(res.status);
                });

                it('should handle empty arrays in email payload', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as jest.Mock).mockResolvedValue([
                        { _id: '507f1f77bcf86cd799439012', name: 'Test Vessel' }
                    ] as never);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([
                        { domain: 'example.com' }
                    ] as never);
                    (NotificationAlert.create as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439011',
                        super_category: [],
                        sub_category: [],
                        country_flags: [],
                        type: 'email',
                        title: [],
                        vessel_ids: ['507f1f77bcf86cd799439012'],
                        receivers: ['<EMAIL>'],
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString(),
                        is_enabled: true,
                        created_by: '507f1f77bcf86cd799439013'
                    } as never);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: [],
                            sub_category: [],
                            country_flags: [],
                            type: 'email',
                            title: [],
                            vessel_ids: ['507f1f77bcf86cd799439012'],
                            receivers: ['<EMAIL>']
                        });

                    expect([400, 500]).toContain(res.status);
                });

                it('should execute email sending logic with proper payload', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as jest.Mock).mockResolvedValue([
                        { _id: '507f1f77bcf86cd799439012', name: 'Test Vessel' }
                    ] as never);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([
                        { domain: 'example.com' }
                    ] as never);

                    (NotificationAlert.create as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439011',
                        super_category: ['Ship'],
                        sub_category: ['Battleship'],
                        country_flags: ['US'],
                        type: 'email',
                        title: ['Test Alert'],
                        vessel_ids: ['507f1f77bcf86cd799439012'],
                        receivers: ['<EMAIL>'],
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString(),
                        is_enabled: true,
                        created_by: '507f1f77bcf86cd799439013'
                    } as never);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'email',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012'],
                            receivers: ['<EMAIL>']
                        });

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('_id');
                });

                it('should handle vessel_ids with "all" value', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as jest.Mock).mockResolvedValue([] as never);
                    (NotificationAlert.create as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439011',
                        super_category: ['Ship'],
                        sub_category: ['Battleship'],
                        country_flags: ['US'],
                        type: 'app',
                        title: ['Test Alert'],
                        vessel_ids: ['all'],
                        receivers: [],
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString(),
                        is_enabled: true,
                        created_by: '507f1f77bcf86cd799439013'
                    } as never);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'app',
                            title: ['Test Alert'],
                            vessel_ids: ['all']
                        });

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('_id');
                });

                it('should handle is_enabled as 0', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as jest.Mock).mockResolvedValue([
                        { _id: '507f1f77bcf86cd799439012', name: 'Test Vessel' }
                    ] as never);
                    (NotificationAlert.create as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439011',
                        super_category: ['Ship'],
                        sub_category: ['Battleship'],
                        country_flags: ['US'],
                        type: 'app',
                        title: ['Test Alert'],
                        vessel_ids: ['507f1f77bcf86cd799439012'],
                        receivers: [],
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString(),
                        is_enabled: false,
                        created_by: '507f1f77bcf86cd799439013'
                    } as never);

                    const res = await request(app).post('/api/notificationsAlerts')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'app',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012'],
                            is_enabled: 0
                        });

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('_id');
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('PATCH /api/notificationsAlerts/:id', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                    (canAccessVessel as jest.Mock).mockResolvedValue(true as never);
                    (vesselService.find as jest.Mock).mockResolvedValue([
                        { _id: '507f1f77bcf86cd799439012', name: 'Test Vessel' }
                    ] as never);
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if the user does not have the required permissions', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011').set('Authorization', nonAuthToken);

                    expect(res.status).toBe(403);
                });

                it('should return 400 if id is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app).patch('/api/notificationsAlerts/invalid-id')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'email',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012']
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 200 and update notification alert successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (NotificationAlert.findByIdAndUpdate as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439011',
                        super_category: ['Ship'],
                        sub_category: ['Battleship'],
                        country_flags: ['US'],
                        type: 'email',
                        title: ['Test Alert'],
                        vessel_ids: ['507f1f77bcf86cd799439012'],
                        receivers: [],
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString(),
                        is_enabled: true,
                        created_by: '507f1f77bcf86cd799439013'
                    } as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'email',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012']
                        });

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('_id');
                });

                it('should return 500 on internal error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (NotificationAlert.findByIdAndUpdate as jest.Mock).mockRejectedValue(new Error('Database error') as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'email',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012']
                        });

                    expect(res.status).toBe(500);
                });

                it('should return 403 if user has no email for email type', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (User.findById as jest.Mock).mockResolvedValue({ _id: userOrApiKey.authorized._id, email: null } as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({
                            type: 'email',
                            receivers: ['<EMAIL>']
                        });

                    expect([403, 500]).toContain(res.status);
                });

                it('should return 403 if user tries to update with unauthorized vessel', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as jest.Mock).mockResolvedValue([
                        { _id: '507f1f77bcf86cd799439012', name: 'Test Vessel' }
                    ] as never);
                    (canAccessVessel as jest.Mock).mockReturnValue(false);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({
                            vessel_ids: ['507f1f77bcf86cd799439012']
                        });

                    expect(res.status).toBe(403);
                });

                it('should return 400 if receiver email domain is not allowed on PATCH', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([
                        { domain: 'example.com' }
                    ] as never);
                    (NotificationAlert.findById as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439011',
                        receivers: [],
                        type: 'email',
                        is_enabled: true,
                        super_category: ['Ship'],
                        sub_category: ['Battleship'],
                        country_flags: ['US'],
                        title: ['Test Alert']
                    } as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({
                            receivers: ['<EMAIL>']
                        });

                    expect(res.status).toBe(400);
                });

                it('should return 404 if notification alert not found when receivers provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([
                        { domain: 'example.com' }
                    ] as never);
                    (NotificationAlert.findById as jest.Mock).mockResolvedValue(null as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({
                            receivers: ['<EMAIL>']
                        });

                    expect([404, 500]).toContain(res.status);
                });

                it('should return 400 on invalid receiver email format', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ receivers: ['invalid-email'] });
                    expect(res.status).toBe(400);
                });

                it('should return 400 when user has no email but receivers provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([{ domain: 'example.com' }] as never);
                    (User.findById as jest.Mock).mockResolvedValue({ _id: userOrApiKey.authorized._id, email: null } as never);
                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ receivers: ['<EMAIL>'] });
                    expect([400, 500]).toContain(res.status);
                });

                it('should return 400 when user email domain is not allowed', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([{ domain: 'allowed.com' }] as never);
                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ receivers: ['<EMAIL>'] });
                    expect(res.status).toBe(400);
                });

                it('should allow additional receiver domains if user has privilege', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([{ domain: 'allowed.com' }, { domain: 'example.com' }] as never);
                    (NotificationAlert.findById as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439011',
                        receivers: ['<EMAIL>'],
                        type: 'email',
                        is_enabled: true,
                        super_category: ['Ship'],
                        sub_category: ['Battleship'],
                        country_flags: ['US'],
                        title: ['Test Alert']
                    } as never);
                    (NotificationAlert.findByIdAndUpdate as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439011', receivers: ['<EMAIL>', '<EMAIL>'], is_enabled: true
                    } as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ receivers: ['<EMAIL>', '<EMAIL>'] });

                    expect([200, 500]).toContain(res.status);
                });

                it('should not send emails when there are no new receivers', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([{ domain: 'example.com' }] as never);
                    (NotificationAlert.findById as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439011', receivers: ['<EMAIL>'], type: 'email', is_enabled: true,
                        super_category: ['Ship'], sub_category: ['Battleship'], country_flags: ['US'], title: ['Test Alert']
                    } as never);
                    (NotificationAlert.findByIdAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011' } as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ receivers: ['<EMAIL>'] });

                    expect([200, 500]).toContain(res.status);
                });

                it('should not send emails when type is app', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([{ domain: 'example.com' }] as never);
                    (NotificationAlert.findById as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439011', receivers: [], type: 'app', is_enabled: true,
                        super_category: ['Ship'], sub_category: ['Battleship'], country_flags: ['US'], title: ['Test Alert']
                    } as never);
                    (NotificationAlert.findByIdAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011' } as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ receivers: ['<EMAIL>'], type: 'app' });

                    expect([200, 500]).toContain(res.status);
                });

                it('should not send emails when alert is disabled even with new receivers', async () => {
                    const user = {
                        ...userOrApiKey.authorized,
                        permissions: [
                            {
                                _id: "679240e27524efb8a6e0cf66",
                                permission_id: 700,
                                permission_name: "MANAGE_NOTIFICATIONS_ALERTS",
                                permission_description: "User can manage notifications alerts",
                                assignable: true
                            }
                        ],
                    };
                    setupAuthorizedAuthMocks(authMethod, { authorized: user, nonAuthorized: userOrApiKey.nonAuthorized }, authToken, User, ApiKey);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([{ domain: 'example.com' }] as never);
                    (NotificationAlert.findById as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439011',
                        receivers: ['<EMAIL>'],
                        type: 'email',
                        is_enabled: true,
                        super_category: [],
                        sub_category: [],
                        country_flags: [],
                        title: []
                    } as never);
                    (NotificationAlert.findByIdAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011' } as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ receivers: ['<EMAIL>', '<EMAIL>'] });

                    expect([200, 500]).toContain(res.status);
                });

                it('should return 400 if type is null', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (NotificationAlert.findByIdAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011', vessel_ids: ['all'] } as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ type: null });

                    expect(res.status).toBe(400);
                })

                it('should return 400 if country_flags is null', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (NotificationAlert.findByIdAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011', vessel_ids: ['all'] } as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ country_flags: null });

                    expect(res.status).toBe(400);
                })

                it('should return 400 if title is empty array', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (NotificationAlert.findByIdAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011', vessel_ids: ['all'] } as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ title: [] });

                    expect(res.status).toBe(400);
                })

                it('should return 400 if title is not array', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (NotificationAlert.findByIdAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011', vessel_ids: ['all'] } as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ title: null });

                    expect(res.status).toBe(400);
                })

                it('should return 400 if sub_category is empty array', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (NotificationAlert.findByIdAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011', vessel_ids: ['all'] } as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ sub_category: [] });

                    expect(res.status).toBe(400);
                })

                it('should return 400 if sub_category is not array', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (NotificationAlert.findByIdAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011', vessel_ids: ['all'] } as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ sub_category: null });

                    expect(res.status).toBe(400);
                })

                it('should return 400 if super_category is empty array', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (NotificationAlert.findByIdAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011', vessel_ids: ['all'] } as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ super_category: [] });

                    expect(res.status).toBe(400);
                })

                it('should return 400 if super_category is not array', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (NotificationAlert.findByIdAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011', vessel_ids: ['all'] } as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ super_category: null });

                    expect(res.status).toBe(400);
                })

                it('should return 400 if receivers is not array', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (NotificationAlert.findByIdAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011', vessel_ids: ['all'] } as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ receivers: 'invalid-receivers' });

                    expect(res.status).toBe(400);
                });

                it('should return 403 user dont have email', async () => {
                    const user = {
                        ...userOrApiKey.authorized,
                        email: null
                    };
                    setupAuthorizedAuthMocks(authMethod, {authorized: user, nonAuthorized: userOrApiKey.nonAuthorized}, authToken, User, ApiKey);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([
                        { domain: 'example.com' }
                    ] as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ type: 'email' });
                    const res2 = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ receivers: ['<EMAIL>'] });

                    expect(res.status).toBe(403);
                    expect(res2.status).toBe(400);
                });

                it('should process vessel_ids containing "all" without converting', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (NotificationAlert.findByIdAndUpdate as jest.Mock).mockResolvedValue({ _id: '507f1f77bcf86cd799439011', vessel_ids: ['all'] } as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ vessel_ids: ['all'] });

                    expect([200, 500]).toContain(res.status);
                });

                it('should return 400 for invalid vessel_ids.*', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ vessel_ids: ['invalid-object-id'] });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for empty vessel_ids', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ vessel_ids: [] });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid vessel_ids.*', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ vessel_ids: null });

                    expect(res.status).toBe(400);
                });

                it('should handle PATCH with receivers and send emails to new receivers', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as jest.Mock).mockResolvedValue([
                        { _id: '507f1f77bcf86cd799439012', name: 'Test Vessel' }
                    ] as never);
                    (EmailDomains.find as jest.Mock).mockResolvedValue([
                        { domain: 'example.com' }
                    ] as never);
                    (NotificationAlert.findById as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439011',
                        receivers: ['<EMAIL>'],
                        type: 'email',
                        is_enabled: true,
                        super_category: ['Ship'],
                        sub_category: ['Battleship'],
                        country_flags: ['US'],
                        title: ['Test Alert']
                    } as never);
                    (NotificationAlert.findByIdAndUpdate as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439011',
                        super_category: ['Ship'],
                        sub_category: ['Battleship'],
                        country_flags: ['US'],
                        type: 'email',
                        title: ['Test Alert'],
                        vessel_ids: ['507f1f77bcf86cd799439012'],
                        receivers: ['<EMAIL>', '<EMAIL>'],
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString(),
                        is_enabled: true,
                        created_by: '507f1f77bcf86cd799439013'
                    } as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'email',
                            title: ['Test Alert'],
                            vessel_ids: ['507f1f77bcf86cd799439012'],
                            receivers: ['<EMAIL>', '<EMAIL>']
                        });

                    expect([200, 500]).toContain(res.status);
                });

                it('should handle PATCH with no vessel_ids', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (NotificationAlert.findByIdAndUpdate as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439011',
                        super_category: ['Ship'],
                        sub_category: ['Battleship'],
                        country_flags: ['US'],
                        type: 'email',
                        title: ['Test Alert'],
                        receivers: [],
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString(),
                        is_enabled: true,
                        created_by: '507f1f77bcf86cd799439013'
                    } as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({
                            super_category: ['Ship'],
                            sub_category: ['Battleship'],
                            country_flags: ['US'],
                            type: 'email',
                            title: ['Test Alert']
                        });

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('_id');
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('PATCH /api/notificationsAlerts/:id/enable', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011/enable');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if the user does not have the required permissions', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011/enable').set('Authorization', nonAuthToken);

                    expect(res.status).toBe(403);
                });

                it('should return 400 if id is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app).patch('/api/notificationsAlerts/invalid-id/enable')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 200 and enable notification alert successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (NotificationAlert.findByIdAndUpdate as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439011',
                        super_category: ['Ship'],
                        sub_category: ['Battleship'],
                        country_flags: ['US'],
                        type: 'email',
                        title: ['Test Alert'],
                        vessel_ids: ['507f1f77bcf86cd799439012'],
                        receivers: [],
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString(),
                        is_enabled: true,
                        created_by: '507f1f77bcf86cd799439013'
                    } as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011/enable')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('_id');
                    expect(res.body.is_enabled).toBe(true);
                });

                it('should return 500 on internal error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (NotificationAlert.findByIdAndUpdate as jest.Mock).mockRejectedValue(new Error('Database error') as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011/enable')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('PATCH /api/notificationsAlerts/:id/disable', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011/disable');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if the user does not have the required permissions', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011/disable').set('Authorization', nonAuthToken);

                    expect(res.status).toBe(403);
                });

                it('should return 400 if id is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app).patch('/api/notificationsAlerts/invalid-id/disable')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 200 and disable notification alert successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (NotificationAlert.findByIdAndUpdate as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439011',
                        super_category: ['Ship'],
                        sub_category: ['Battleship'],
                        country_flags: ['US'],
                        type: 'email',
                        title: ['Test Alert'],
                        vessel_ids: ['507f1f77bcf86cd799439012'],
                        receivers: [],
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString(),
                        is_enabled: false,
                        created_by: '507f1f77bcf86cd799439013'
                    } as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011/disable')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('_id');
                    expect(res.body.is_enabled).toBe(false);
                });

                it('should return 500 on internal error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (NotificationAlert.findByIdAndUpdate as jest.Mock).mockRejectedValue(new Error('Database error') as never);

                    const res = await request(app).patch('/api/notificationsAlerts/507f1f77bcf86cd799439011/disable')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('DELETE /api/notificationsAlerts/:id', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).delete('/api/notificationsAlerts/507f1f77bcf86cd799439011');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if the user does not have the required permissions', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);

                    const res = await request(app).delete('/api/notificationsAlerts/507f1f77bcf86cd799439011').set('Authorization', nonAuthToken);

                    expect(res.status).toBe(403);
                });

                it('should return 400 if id is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app).delete('/api/notificationsAlerts/invalid-id')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 200 and delete notification alert successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (NotificationAlert.findByIdAndDelete as jest.Mock).mockResolvedValue({
                        _id: '507f1f77bcf86cd799439011',
                        super_category: ['Ship'],
                        sub_category: ['Battleship'],
                        country_flags: ['US'],
                        type: 'email',
                        title: ['Test Alert'],
                        vessel_ids: ['507f1f77bcf86cd799439012'],
                        receivers: [],
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString(),
                        is_enabled: true,
                        created_by: '507f1f77bcf86cd799439013'
                    } as never);

                    const res = await request(app).delete('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('_id');
                });

                it('should return 500 on internal error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (NotificationAlert.findByIdAndDelete as jest.Mock).mockRejectedValue(new Error('Database error') as never);

                    const res = await request(app).delete('/api/notificationsAlerts/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/notificationsAlerts/unsubscribe/email', () => {

        let verifySpy: ReturnType<typeof jest.spyOn>;
        beforeEach(() => {
            jest.resetAllMocks();
            (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
            });
            verifySpy = jest.spyOn(jwt as any, 'verify').mockImplementation((...args: unknown[]) => {
                const token = args[0] as string;
                try { return JSON.parse(token); } catch { return {}; }
            });
        });

        afterEach(() => { verifySpy.mockRestore(); });

        it('should return 400 if token is missing', async () => {
            const res = await request(app).get('/api/notificationsAlerts/unsubscribe/email');
            expect(res.status).toBe(400);
        });

        it('should redirect if notification alert does not exist', async () => {
            (NotificationAlert.findById as jest.Mock).mockResolvedValue(null as never);
            const res = await request(app).get('/api/notificationsAlerts/unsubscribe/email').query({ token: JSON.stringify({ notificationId: '507f1f77bcf86cd799439011', email: '<EMAIL>' }) });
            expect(res.status).toBe(302);
        });

        it('should return 200 and unsubscribe successfully', async () => {
            const token = JSON.stringify({ notificationId: '507f1f77bcf86cd799439011', email: '<EMAIL>' });
            (NotificationAlert.findById as jest.Mock).mockResolvedValue({
                _id: '507f1f77bcf86cd799439011',
                receivers: ['<EMAIL>'],
                created_by: '507f1f77bcf86cd799439013'
            } as never);
            (NotificationAlert.updateOne as jest.Mock).mockResolvedValue({ modifiedCount: 1 } as never);

            const res = await request(app).get(`/api/notificationsAlerts/unsubscribe/email?token=${token}`);
            expect(res.status).toBe(302);
        });

        it('should return 500 on internal error', async () => {
            const token = JSON.stringify({ notificationId: '507f1f77bcf86cd799439011', email: '<EMAIL>' });
            (NotificationAlert.findById as jest.Mock).mockRejectedValue(new Error('Database error') as never);

            const res = await request(app).get(`/api/notificationsAlerts/unsubscribe/email?token=${token}`);
            expect(res.status).toBe(500);
        });

        it('should handle unsubscribe when email is not in receivers but user is creator', async () => {
            const token = JSON.stringify({ notificationId: '507f1f77bcf86cd799439011', email: '<EMAIL>' });
            (NotificationAlert.findById as jest.Mock).mockResolvedValue({
                _id: '507f1f77bcf86cd799439011',
                receivers: ['<EMAIL>'],
                created_by: '507f1f77bcf86cd799439013'
            } as never);
            (User.findOne as jest.Mock).mockResolvedValue({
                _id: '507f1f77bcf86cd799439013',
                email: '<EMAIL>'
            } as never);
            (NotificationAlert.updateOne as jest.Mock).mockResolvedValue({ modifiedCount: 1 } as never);

            const res = await request(app).get(`/api/notificationsAlerts/unsubscribe/email?token=${token}`);
            expect(res.status).toBe(302);
        });

        it('should handle unsubscribe when email is not in receivers and user is not creator', async () => {
            const token = JSON.stringify({ notificationId: '507f1f77bcf86cd799439011', email: '<EMAIL>' });
            (NotificationAlert.findById as jest.Mock).mockResolvedValue({
                _id: '507f1f77bcf86cd799439011',
                receivers: ['<EMAIL>'],
                created_by: '507f1f77bcf86cd799439013'
            } as never);
            (User.findOne as jest.Mock).mockResolvedValue(null as never);

            const res = await request(app).get(`/api/notificationsAlerts/unsubscribe/email?token=${token}`);
            expect(res.status).toBe(302);
        });

        it('should handle unsubscribe when updateOne returns no modifiedCount', async () => {
            const token = JSON.stringify({ notificationId: '507f1f77bcf86cd799439011', email: '<EMAIL>' });
            (NotificationAlert.findById as jest.Mock).mockResolvedValue({
                _id: '507f1f77bcf86cd799439011',
                receivers: ['<EMAIL>'],
                created_by: '507f1f77bcf86cd799439013'
            } as never);
            (NotificationAlert.updateOne as jest.Mock).mockResolvedValue({ modifiedCount: 0 } as never);

            const res = await request(app).get(`/api/notificationsAlerts/unsubscribe/email?token=${token}`);
            expect(res.status).toBe(302);
        });
    });

    describe('GET /api/notificationsAlerts/map', () => {

        let verifySpy: ReturnType<typeof jest.spyOn>;
        beforeEach(() => {
            jest.resetAllMocks();
            (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
            });
            verifySpy = jest.spyOn(jwt as any, 'verify').mockImplementation((...args: unknown[]) => {
                const token = args[0] as string;
                try { return JSON.parse(token); } catch { return {}; }
            });
        });
        afterEach(() => { verifySpy.mockRestore(); });

        it('should return 400 if token is missing', async () => {
            const res = await request(app).get('/api/notificationsAlerts/map');
            expect(res.status).toBe(400);
        });

        it('should return 200 and serve map image successfully', async () => {
            const token = JSON.stringify({ locations: [[40.7128, -74.0060]] });
            (buildStaticMarkerSignature as jest.Mock).mockReturnValue('marker-signature');
            const closedStream = new ReadableStream({ start(controller) { controller.close(); } });
            (getStaticMapOld as jest.Mock).mockResolvedValue({
                mimeType: 'image/png',
                source: {
                    headers: { get: jest.fn().mockReturnValue('0') },
                    body: closedStream
                }
            } as never);

            const res = await request(app).get(`/api/notificationsAlerts/map?token=${token}`);
            expect(res.status).toBe(200);
        });

        it('should return 500 on internal error', async () => {
            const token = JSON.stringify({ locations: [[40.7128, -74.0060]] });
            (buildStaticMarkerSignature as jest.Mock).mockImplementation(() => { throw new Error('Map error'); });

            const res = await request(app).get(`/api/notificationsAlerts/map?token=${token}`);
            expect(res.status).toBe(500);
        });

        it('should return 500 if locations is missing', async () => {
            const token = JSON.stringify({ locations: null });

            const res = await request(app).get(`/api/notificationsAlerts/map?token=${token}`);
            expect(res.status).toBe(500);
        });

        it('should return 500 if mapData is invalid', async () => {
            const token = JSON.stringify({ locations: [[40.7128, -74.0060]] });
            (buildStaticMarkerSignature as jest.Mock).mockReturnValue('marker-signature');
            (getStaticMapOld as jest.Mock).mockResolvedValue(null as never);

            const res = await request(app).get(`/api/notificationsAlerts/map?token=${token}`);
            expect(res.status).toBe(500);
        });

        it('should return 500 if mapData has no source', async () => {
            const token = JSON.stringify({ locations: [[40.7128, -74.0060]] });
            (buildStaticMarkerSignature as jest.Mock).mockReturnValue('marker-signature');
            (getStaticMapOld as jest.Mock).mockResolvedValue({
                mimeType: 'image/png',
                source: null
            } as never);

            const res = await request(app).get(`/api/notificationsAlerts/map?token=${token}`);
            expect(res.status).toBe(500);
        });
    });

    describe('GET /api/notificationsAlerts/cluster/:amount', () => {

        beforeEach(() => {
            jest.resetAllMocks();
        });

        it('should return 400 if amount is invalid', async () => {
            const res = await request(app).get('/api/notificationsAlerts/cluster/invalid');
            expect(res.status).toBe(400);
        });

        it('should return 200 and serve cluster image successfully', async () => {
            (generateNumberedIconBuffer as jest.Mock).mockResolvedValue(Buffer.from('test-image-data') as never);

            const res = await request(app).get('/api/notificationsAlerts/cluster/5?bgColor=red&textColor=white');
            expect(res.status).toBe(200);
        });

        it('should return 500 on internal error', async () => {
            (generateNumberedIconBuffer as jest.Mock).mockRejectedValue(new Error('Image generation error') as never);

            const res = await request(app).get('/api/notificationsAlerts/cluster/5');
            expect(res.status).toBe(500);
        });

        it('should return 500 if buffer is null', async () => {
            (generateNumberedIconBuffer as jest.Mock).mockResolvedValue(null as never);

            const res = await request(app).get('/api/notificationsAlerts/cluster/5');
            expect(res.status).toBe(500);
        });
    });

    describe('GET /api/notificationsAlerts/testing', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/notificationsAlerts/testing');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if the user does not have the required permissions', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);

                    const res = await request(app).get('/api/notificationsAlerts/testing').set('Authorization', nonAuthToken);

                    expect(res.status).toBe(403);
                });

                it('should return 200 and trigger notifications successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (microservice_socket.emit as jest.Mock).mockReturnValue(true);

                    const res = await request(app).get('/api/notificationsAlerts/testing').set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('message');
                    expect(microservice_socket.emit).toHaveBeenCalledWith('testing/triggerNotifications', expect.any(String));
                });

                it('should return 500 on internal error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (microservice_socket.emit as jest.Mock).mockImplementation(() => {
                        throw new Error('Socket error');
                    });

                    const res = await request(app).get('/api/notificationsAlerts/testing').set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});
