import User from '../../models/User';
import { usersList } from '../data/Users';
import { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } from '../data/Auth';
import ApiKey from '../../models/ApiKey';
import Role from '../../models/Role';
import InviteToken from '../../models/InviteToken';
import Organization from '../../models/Organization';
import Vessel from '../../models/Vessel';
import mongoose from 'mongoose';
import crypto from 'crypto';
import request from 'supertest';
import app from '../../server';
import * as utilsFunctions from '../../utils/functions';
import * as otpService from '../../modules/otpService';
import jwt from 'jsonwebtoken';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import { sendEmail } from '../../modules/email';
import { setupAuthMocks, setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';
import bcrypt from 'bcryptjs';
import { getUser } from '../../queries/User';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../models/Role', () => require('../mocks/models/role.mock'));
jest.mock('../../models/InviteToken', () => require('../mocks/models/inviteToken.mock'));
jest.mock('../../models/Organization', () => require('../mocks/models/organization.mock'));
jest.mock('../../models/Vessel', () => require('../mocks/models/vessel.mock'));
jest.mock('../../modules/email', () => require('../mocks/modules/email.mock'));
jest.mock('../../modules/otpService', () => require('../mocks/modules/otpService.mock'));
jest.mock('../../utils/functions', () => require('../mocks/utils/functions.mock'));
jest.mock('bcryptjs', () => require('../mocks/utils/bcryptjs.mock'));
jest.mock('jsonwebtoken', () => require('../mocks/jwt.mock'));
jest.mock('../../queries/User', () => require('../mocks/queries/user.mock'));

describe('Users API', () => {

    describe('GET /api/auth', () => {

        const runTests = (authMethod, header, authValue, userOrApiKey, mockAuthorizationModel) => {
            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                    (utilsFunctions.validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                    (utilsFunctions.escapeRegExp as jest.Mock).mockImplementation((str: any) => {
                        return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                    });
                    (bcrypt.compare as jest.Mock).mockResolvedValue(true as never);
                    (jwt.sign as jest.Mock).mockReturnValue('mock-token' as never);
                });

                if (authMethod === 'user') {
                    it('should return 401 if auth header is not Basic', async () => {
                        const res = await request(app).get('/api/users/auth').set('Authorization', 'Bearer some-value');
                        expect(res.status).toBe(401);
                    });

                    it('should return 400 if auth header does not contain username or password', async () => {
                        const res = await request(app).get('/api/users/auth').set('Authorization', `Basic ${btoa(`:`)}`);
                        expect(res.status).toBe(400);
                    });

                    it('should return 400 if username is invalid', async () => {
                        jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(null as any);
                        const res = await request(app).get('/api/users/auth').set(header, authValue());
                        expect(res.status).toBe(400);
                    });

                    it('should return 302 status if email_verification_enabled is true and email_verified_device_ids is empty', async () => {
                        const mockUser = {
                            email: '<EMAIL>',
                            email_verified_device_ids: [],
                            email_verification_enabled: true
                        };
                        mockAuthorizationModel.mockResolvedValueOnce(userOrApiKey);
                        jest.spyOn(User as any, 'find').mockResolvedValue(mockUser as any);
                        jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(mockUser as any);
                        const res = await request(app).get('/api/users/auth').set(header, authValue());
                        expect(res.status).toBe(302);
                    });

                    it('should proceed with authentication if email_verification_enabled is true but device is already verified', async () => {
                        const mockUser = {
                            email: '<EMAIL>',
                            email_verified_device_ids: ['device123'],
                            email_verification_enabled: true,
                            jwt_tokens: [],
                            save: (jest.fn() as any).mockResolvedValue({} as any)
                        };
                        mockAuthorizationModel.mockResolvedValueOnce(mockUser);
                        (bcrypt.compare as jest.Mock).mockResolvedValueOnce(true as never);
                        const res = await request(app).get('/api/users/auth').set(header, authValue()).set('Cookie', 'deviceId=device123');
                        expect(res.status).toBe(200);
                        expect(res.body).toHaveProperty('jwt_token');
                        expect(res.body).toHaveProperty('expires');
                    });
                }

                if (authMethod === 'api-key') {
                    it('should return 400 if api key has been revoked', async () => {
                        let _userOrApiKey = { ...userOrApiKey }
                        _userOrApiKey.is_revoked = true
                        mockAuthorizationModel.mockResolvedValueOnce(_userOrApiKey);
                        const res = await request(app).get('/api/users/auth').set(header, authValue());
                        expect(res.status).toBe(400);
                    });
                    it('should return 400 if api key has been deleted', async () => {
                        let _userOrApiKey = { ...userOrApiKey }
                        _userOrApiKey.is_deleted = true
                        mockAuthorizationModel.mockResolvedValueOnce(_userOrApiKey);
                        const res = await request(app).get('/api/users/auth').set(header, authValue());
                        expect(res.status).toBe(400);
                    });
                    it('should return 500 if JWT_SECRET is not set for API key', async () => {
                        const originalSecret = process.env.JWT_SECRET;
                        delete process.env.JWT_SECRET;

                        const mockApiKey = JSON.parse(JSON.stringify(userOrApiKey, (_key, value) => value));
                        (mockApiKey as any).save = (jest.fn() as any).mockResolvedValue({} as any);
                        mockAuthorizationModel.mockResolvedValueOnce(mockApiKey);

                        const res = await request(app).get('/api/users/auth').set(header, authValue());
                        expect(res.status).toBe(500);
                        expect(res.body.message).toBe('Internal server error');

                        process.env.JWT_SECRET = originalSecret;
                    });

                    it('should successfully authenticate with API key and save JWT token', async () => {
                        const mockApiKey = JSON.parse(JSON.stringify(userOrApiKey, (_key, value) => value));
                        (mockApiKey as any).save = (jest.fn() as any).mockResolvedValue({} as any);
                        mockAuthorizationModel.mockResolvedValueOnce(mockApiKey);

                        const res = await request(app).get('/api/users/auth').set(header, authValue());
                        expect(res.status).toBe(200);
                        expect(res.body).toHaveProperty('jwt_token');
                        expect(res.body).toHaveProperty('expires');
                        expect((mockApiKey as any).save).toHaveBeenCalled();
                    });
                }

                it('should return 401 if no auth header is provided', async () => {
                    const res = await request(app).get('/api/users/auth');
                    expect(res.status).toBe(401);
                });

                it('should return 400 or 401 if invalid credentails are provided', async () => {
                    let _userOrApiKey = { ...userOrApiKey }
                    _userOrApiKey.password = 'invalid-password-hash'
                    _userOrApiKey.email_verification_enabled = false
                    if (authMethod === 'api-key')
                        _userOrApiKey = null
                    mockAuthorizationModel.mockResolvedValueOnce(_userOrApiKey);
                    (bcrypt.compare as jest.Mock).mockResolvedValueOnce(false as never);
                    const res = await request(app).get('/api/users/auth').set(header, authValue());
                    expect([400, 401]).toContain(res.status);
                });

                it('should return the jwt token along with expiration', async () => {
                    const mockUser = JSON.parse(JSON.stringify(userOrApiKey, (_key, value) => value));
                    mockUser.email_verification_enabled = false;
                    mockUser.jwt_tokens = [];
                    (mockUser as any).save = (jest.fn() as any).mockResolvedValue({} as any);
                    mockAuthorizationModel.mockResolvedValueOnce(mockUser);
                    (bcrypt.compare as jest.Mock).mockResolvedValueOnce(true as never);
                    const res = await request(app).get('/api/users/auth').set(header, authValue());
                    expect(res.status).toBe(200);
                    expect(res.body).toBeInstanceOf(Object);
                    ['jwt_token', 'expires'].forEach(prop => {
                        expect(res.body).toHaveProperty(prop);
                    });
                });

                it('should not shift tokens if tokens length is less than 10', async () => {
                    const mockUser = JSON.parse(JSON.stringify(userOrApiKey, (_key, value) => value));
                    mockUser.email_verification_enabled = false;
                    mockUser.jwt_tokens = ['token1', 'token2', 'token3', 'token4', 'token5', 'token6'];
                    (mockUser as any).save = (jest.fn() as any).mockResolvedValue({} as any);
                    mockAuthorizationModel.mockResolvedValueOnce(mockUser);
                    (bcrypt.compare as jest.Mock).mockResolvedValueOnce(true as never);
                    const res = await request(app).get('/api/users/auth').set(header, authValue());
                    expect(res.status).toBe(200);
                    expect(res.body).toBeInstanceOf(Object);
                    ['jwt_token', 'expires'].forEach(prop => {
                        expect(res.body).toHaveProperty(prop);
                    });
                });

                it('should shift tokens if tokens length is 10 or more', async () => {
                    const mockUser = JSON.parse(JSON.stringify(userOrApiKey, (_key, value) => value));
                    mockUser.email_verification_enabled = false;
                    mockUser.jwt_tokens = ['token1', 'token2', 'token3', 'token4', 'token5', 'token6', 'token7', 'token8', 'token9', 'token10'];
                    (mockUser as any).save = (jest.fn() as any).mockResolvedValue({} as any);
                    mockAuthorizationModel.mockResolvedValueOnce(mockUser);
                    (bcrypt.compare as jest.Mock).mockResolvedValueOnce(true as never);
                    const res = await request(app).get('/api/users/auth').set(header, authValue());
                    expect(res.status).toBe(200);
                    expect(res.body).toBeInstanceOf(Object);
                    ['jwt_token', 'expires'].forEach(prop => {
                        expect(res.body).toHaveProperty(prop);
                    });
                    expect(mockUser.jwt_tokens.length).toBe(10);
                });

                it('should return 500 if JWT_SECRET is not set', async () => {
                    const originalSecret = process.env.JWT_SECRET;
                    delete process.env.JWT_SECRET;

                    const mockUser = JSON.parse(JSON.stringify(userOrApiKey, (_key, value) => value));
                    mockUser.email_verification_enabled = false;
                    mockUser.jwt_tokens = [];
                    (mockUser as any).save = (jest.fn() as any).mockResolvedValue({} as any);
                    mockAuthorizationModel.mockResolvedValueOnce(mockUser);
                    (bcrypt.compare as jest.Mock).mockResolvedValueOnce(true as never);

                    const res = await request(app).get('/api/users/auth').set(header, authValue());
                    expect(res.status).toBe(500);
                    expect(res.body.message).toBe('Internal server error');

                    process.env.JWT_SECRET = originalSecret;
                });

                it('should handle internal server errors', async () => {
                    mockAuthorizationModel.mockRejectedValueOnce(new Error('Something went wrong'));

                    const res = await request(app).get('/api/users/auth').set(header, authValue());

                    expect(res.status).toBe(500);
                });

                it('should handle database errors during user save', async () => {
                    const mockUser = JSON.parse(JSON.stringify(userOrApiKey, (_key, value) => value));
                    mockUser.email_verification_enabled = false;
                    mockUser.jwt_tokens = [];
                    (mockUser as any).save = (jest.fn() as any).mockRejectedValueOnce(new Error('Database save error'));
                    mockAuthorizationModel.mockResolvedValueOnce(mockUser);
                    (bcrypt.compare as jest.Mock).mockResolvedValueOnce(true as never);

                    const res = await request(app).get('/api/users/auth').set(header, authValue());

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', 'Authorization', () => `Basic ${btoa(`test:testtest`)}`, { ...authorizedUser, password: '$2a$10$o1PdnWXbuF6kgxkijsyspedX0EwIra2szVjy4Br/X6qWp1Qcw5C7u' }, User.findOne);
        runTests('api-key', 'qm-api-key', () => 'd21e1a57f2de39b3f4fbd42cf871d9bc', authorizedApiKey, ApiKey.findOne);

    })

    describe('GET /api/users/user', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (jwt.verify as jest.Mock).mockReturnValue({ user_id: userOrApiKey.authorized._id });
                    (getUser as jest.Mock).mockResolvedValueOnce({} as never);
                    (utilsFunctions.validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/users/user');
                    expect(res.status).toBe(401);
                });

                it('should return 404 if token has invalid user_id', async () => {
                    jest.resetAllMocks();
                    if (authMethod === 'user') {
                        (jwt.verify as jest.Mock).mockReturnValue({ user_id: userOrApiKey.authorized._id });
                        (getUser as jest.Mock).mockResolvedValueOnce(null as never);
                        const res = await request(app).get('/api/users/user').set('Authorization', authToken);
                        expect(res.status).toBe(404);
                    } else {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                        const res = await request(app).get('/api/users/user').set('Authorization', authToken);
                        expect(res.status).toBe(400);
                    }
                });

                it('should return the user object for user authentication, and 400 for api-key authentication', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    if (authMethod === 'user') {
                        (getUser as jest.Mock).mockResolvedValueOnce({ _id: userOrApiKey.authorized._id, name: 'Test', username: 'test', role_id: 1, deletable: true, is_deleted: false, creation_timestamp: new Date().toISOString(), role: {}, permissions: [] } as never);
                        const res = await request(app).get('/api/users/user').set('Authorization', authToken);
                        expect(res.status).toBe(200);
                        expect(res.body).toBeInstanceOf(Object);
                    } else {
                        const res = await request(app).get('/api/users/user').set('Authorization', authToken);
                        expect(res.status).toBe(400);
                    }
                });

                it('should handle internal server errors', async () => {
                    jest.resetAllMocks();
                    (jwt.verify as jest.Mock).mockReturnValue({ user_id: userOrApiKey.authorized._id });
                    (utilsFunctions.validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                    if (authMethod === 'user') {
                        (jwt.verify as jest.Mock).mockReturnValue({ user_id: userOrApiKey.authorized._id });
                        (getUser as jest.Mock).mockRejectedValueOnce(new Error('Something went wrong') as never);
                        const res = await request(app).get('/api/users/user').set('Authorization', authToken);
                        expect(res.status).toBe(500);
                    } else {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                        const res = await request(app).get('/api/users/user').set('Authorization', authToken);
                        expect(res.status).toBe(400);
                    }
                });

                it('should return 500 if JWT_SECRET is not set', async () => {
                    const originalSecret = process.env.JWT_SECRET;
                    delete process.env.JWT_SECRET;
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app).get('/api/users/user').set('Authorization', authToken);
                    expect(res.status).toBe(500);
                    process.env.JWT_SECRET = originalSecret;
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });

    })

    describe('GET /api/users', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                    (utilsFunctions.validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/users');
                    expect(res.status).toBe(401);
                });

                it('should handle internal server errors', async () => {
                    (jwt.verify as jest.Mock).mockReturnValue({ user_id: userOrApiKey.authorized._id });
                    const tokenOnly = authToken.replace('Bearer ', '');
                    const authedUser = { ...userOrApiKey.authorized, jwt_tokens: [tokenOnly] };
                    (getUser as jest.Mock).mockResolvedValueOnce(authedUser as never);
                    jest.spyOn(User as any, 'countDocuments').mockRejectedValueOnce(new Error('Something went wrong'));
                    const res = await request(app).get('/api/users').set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });

                it('should return 500 if JWT_SECRET is not set', async () => {
                    const originalSecret = process.env.JWT_SECRET;
                    delete process.env.JWT_SECRET;
                    const res = await request(app).get('/api/users').set('Authorization', authToken);
                    expect(res.status).toBe(500);
                    process.env.JWT_SECRET = originalSecret;
                });

                it('should handle pagination with custom page and limit', async () => {
                    (jwt.verify as jest.Mock).mockReturnValue({ user_id: userOrApiKey.authorized._id });
                    const tokenOnly = authToken.replace('Bearer ', '');
                    const authedUser = { ...userOrApiKey.authorized, jwt_tokens: [tokenOnly] };
                    (getUser as jest.Mock).mockResolvedValueOnce(authedUser as never);
                    const aggregateSpy = jest.spyOn(User as any, 'aggregate');
                    aggregateSpy.mockResolvedValueOnce(usersList as any);
                    jest.spyOn(User as any, 'countDocuments').mockResolvedValueOnce(5 as any);
                    const res = await request(app).get('/api/users?page=2&limit=3').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('users');
                    expect(res.body).toHaveProperty('totalCount');
                    expect(res.body).toHaveProperty('totalPages');
                });

                it('should handle invalid page parameter (less than 1)', async () => {
                    (jwt.verify as jest.Mock).mockReturnValue({ user_id: userOrApiKey.authorized._id });
                    const tokenOnly = authToken.replace('Bearer ', '');
                    const authedUser = { ...userOrApiKey.authorized, jwt_tokens: [tokenOnly] };
                    (getUser as jest.Mock).mockResolvedValueOnce(authedUser as never);
                    const aggregateSpy = jest.spyOn(User as any, 'aggregate');
                    aggregateSpy.mockResolvedValueOnce(usersList as any);
                    jest.spyOn(User as any, 'countDocuments').mockResolvedValueOnce(2 as any);
                    const res = await request(app).get('/api/users?page=0&limit=10').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('users');
                    expect(res.body).toHaveProperty('totalCount');
                    expect(res.body).toHaveProperty('totalPages');
                });

                it('should handle invalid limit parameter (less than 1)', async () => {
                    (jwt.verify as jest.Mock).mockReturnValue({ user_id: userOrApiKey.authorized._id });
                    const tokenOnly = authToken.replace('Bearer ', '');
                    const authedUser = { ...userOrApiKey.authorized, jwt_tokens: [tokenOnly] };
                    (getUser as jest.Mock).mockResolvedValueOnce(authedUser as never);
                    const aggregateSpy = jest.spyOn(User as any, 'aggregate');
                    aggregateSpy.mockResolvedValueOnce(usersList as any);
                    jest.spyOn(User as any, 'countDocuments').mockResolvedValueOnce(2 as any);
                    const res = await request(app).get('/api/users?page=1&limit=0').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('users');
                    expect(res.body).toHaveProperty('totalCount');
                    expect(res.body).toHaveProperty('totalPages');
                });

                it('should handle non-internal organization filtering', async () => {
                    (jwt.verify as jest.Mock).mockReturnValue({ user_id: userOrApiKey.authorized._id });
                    const tokenOnly = authToken.replace('Bearer ', '');
                    const authedUser = {
                        ...userOrApiKey.authorized,
                        jwt_tokens: [tokenOnly],
                        organization: { _id: '67e3054d706d97d316aabbe6', is_internal: false }
                    };
                    (getUser as jest.Mock).mockResolvedValueOnce(authedUser as never);
                    const aggregateSpy = jest.spyOn(User as any, 'aggregate');
                    aggregateSpy.mockResolvedValueOnce(usersList as any);
                    jest.spyOn(User as any, 'countDocuments').mockResolvedValueOnce(2 as any);
                    const res = await request(app).get('/api/users').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('users');
                    expect(res.body).toHaveProperty('totalCount');
                    expect(res.body).toHaveProperty('totalPages');
                });

                it('should handle internal organization (no filtering)', async () => {
                    (jwt.verify as jest.Mock).mockReturnValue({ user_id: userOrApiKey.authorized._id });
                    const tokenOnly = authToken.replace('Bearer ', '');
                    const authedUser = {
                        ...userOrApiKey.authorized,
                        jwt_tokens: [tokenOnly],
                        organization: { _id: '67e3054d706d97d316aabbe6', is_internal: true }
                    };
                    (getUser as jest.Mock).mockResolvedValueOnce(authedUser as never);
                    const aggregateSpy = jest.spyOn(User as any, 'aggregate');
                    aggregateSpy.mockResolvedValueOnce(usersList as any);
                    jest.spyOn(User as any, 'countDocuments').mockResolvedValueOnce(2 as any);
                    const res = await request(app).get('/api/users').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('users');
                    expect(res.body).toHaveProperty('totalCount');
                    expect(res.body).toHaveProperty('totalPages');
                });

                it('should handle User.aggregate errors', async () => {
                    (jwt.verify as jest.Mock).mockReturnValue({ user_id: userOrApiKey.authorized._id });
                    const tokenOnly = authToken.replace('Bearer ', '');
                    const authedUser = { ...userOrApiKey.authorized, jwt_tokens: [tokenOnly] };
                    (getUser as jest.Mock).mockResolvedValueOnce(authedUser as never);
                    const aggregateSpy = jest.spyOn(User as any, 'aggregate');
                    aggregateSpy.mockRejectedValueOnce(new Error('Aggregation failed'));
                    jest.spyOn(User as any, 'countDocuments').mockResolvedValueOnce(2 as any);
                    const res = await request(app).get('/api/users').set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });

    })

    describe('POST /api/users', () => {
        beforeEach(() => {
            jest.resetAllMocks();
            (utilsFunctions.validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
            });
            (utilsFunctions.escapeRegExp as jest.Mock).mockImplementation((str: any) => {
                return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            });
            (utilsFunctions.isIntStrict as jest.Mock).mockImplementation((value: any) => {
                return Number.isInteger(value);
            });
            (bcrypt.hash as jest.Mock).mockResolvedValue('hashed-password' as never);
        });

        it('should return 400 if empty object send in body', async () => {
            const res = await request(app)
                .post('/api/users')
                .send({});
            expect(res.status).toBe(400);
        });

        it('should return 400 if email is not associated with the link', async () => {
            const mockInviteToken = { token: 'mock-token', is_used: false };
            jest.spyOn(InviteToken as any, 'findOne').mockResolvedValueOnce(mockInviteToken);
            (jwt.verify as jest.Mock).mockReturnValue({ email: '<EMAIL>', role_id: 1, organization_id: '67e3054d706d97d316aabbe6' });

            const res = await request(app)
                .post('/api/users')
                .send({
                    name: 'John Doe',
                    username: 'johndoe',
                    email: '<EMAIL>',
                    password: 'password123',
                    role_id: 1,
                    organization_id: '67e3054d706d97d316aabbe6',
                    token: 'valid-token',
                });
            expect(res.status).toBe(400);
        });

        it('should return 400 if role_id is not associated with the link', async () => {
            const mockInviteToken = { token: 'mock-token', is_used: false };
            jest.spyOn(InviteToken as any, 'findOne').mockResolvedValueOnce(mockInviteToken);
            (jwt.verify as jest.Mock).mockReturnValue({ email: '<EMAIL>', role_id: 2, organization_id: '67e3054d706d97d316aabbe6' });

            const res = await request(app)
                .post('/api/users')
                .send({
                    name: 'John Doe',
                    username: 'johndoe',
                    email: '<EMAIL>',
                    password: 'password123',
                    role_id: 1,
                    organization_id: '67e3054d706d97d316aabbe6',
                    token: 'valid-token',
                });
            expect(res.status).toBe(400);
        });

        it('should return 400 if organization_id is not associated with the link', async () => {
            const mockInviteToken = { token: 'mock-token', is_used: false };
            jest.spyOn(InviteToken as any, 'findOne').mockResolvedValueOnce(mockInviteToken);
            (jwt.verify as jest.Mock).mockReturnValue({ email: '<EMAIL>', role_id: 1, organization_id: 'org456' });

            const res = await request(app)
                .post('/api/users')
                .send({
                    name: 'John Doe',
                    username: 'johndoe',
                    email: '<EMAIL>',
                    password: 'password123',
                    role_id: 1,
                    organization_id: '67e3054d706d97d316aabbe6',
                    token: 'valid-token',
                });
            expect(res.status).toBe(400);
        });

        it('should return 400 if username is already taken', async () => {
            const mockInviteToken = { token: 'mock-token', is_used: false };
            jest.spyOn(InviteToken as any, 'findOne').mockResolvedValueOnce(mockInviteToken);
            (jwt.verify as jest.Mock).mockReturnValue({ email: '<EMAIL>', role_id: 1, organization_id: '67e3054d706d97d316aabbe6' });
            jest.spyOn(User as any, 'findOne').mockResolvedValueOnce({ username: 'johndoe' } as any);

            const res = await request(app)
                .post('/api/users')
                .send({
                    name: 'John Doe',
                    username: 'johndoe',
                    email: '<EMAIL>',
                    password: 'password123',
                    role_id: 1,
                    organization_id: '67e3054d706d97d316aabbe6',
                    token: 'valid-token',
                });
            expect(res.status).toBe(400);
        });

        it('should return 400 if email is already taken', async () => {
            const mockInviteToken = { token: 'mock-token', is_used: false };
            jest.spyOn(InviteToken as any, 'findOne').mockResolvedValueOnce(mockInviteToken);
            (jwt.verify as jest.Mock).mockReturnValue({ email: '<EMAIL>', role_id: 1, organization_id: '67e3054d706d97d316aabbe6' });
            jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(null as any).mockResolvedValueOnce({ email: '<EMAIL>' } as any);

            const res = await request(app)
                .post('/api/users')
                .send({
                    name: 'John Doe',
                    username: 'johndoe1',
                    email: '<EMAIL>',
                    password: 'password123',
                    role_id: 1,
                    organization_id: '67e3054d706d97d316aabbe6',
                    token: 'valid-token',
                });
            expect(res.status).toBe(400);
        });

        it('should return 404 if the role does not exist', async () => {
            const mockInviteToken = { token: 'mock-token', is_used: false };
            jest.spyOn(InviteToken as any, 'findOne').mockResolvedValueOnce(mockInviteToken);
            (jwt.verify as jest.Mock).mockReturnValue({ email: '<EMAIL>', role_id: 99, organization_id: '67e3054d706d97d316aabbe6' });
            jest.spyOn(User as any, 'findOne').mockResolvedValue(null as any);
            jest.spyOn(Role as any, 'findOne').mockResolvedValue(null as any);
            jest.spyOn(Organization as any, 'findOne').mockResolvedValue({ _id: '67e3054d706d97d316aabbe6' } as any);

            const res = await request(app)
                .post('/api/users')
                .send({
                    name: 'John Doe',
                    username: 'johndoe',
                    email: '<EMAIL>',
                    password: 'password123',
                    role_id: 99,
                    organization_id: '67e3054d706d97d316aabbe6',
                    token: 'valid-token',
                });
            expect(res.status).toBe(404);
        });

        it('should return 404 if the organization does not exist', async () => {
            const mockInviteToken = { token: 'mock-token', is_used: false };
            jest.spyOn(InviteToken as any, 'findOne').mockResolvedValueOnce(mockInviteToken);
            (jwt.verify as jest.Mock).mockReturnValue({ email: '<EMAIL>', role_id: 1, organization_id: '67e3054d706d97d316aabbe6' });
            jest.spyOn(User as any, 'findOne').mockResolvedValue(null as any);
            jest.spyOn(Role as any, 'findOne').mockResolvedValue({ role_id: 1 } as any);
            jest.spyOn(Organization as any, 'findOne').mockResolvedValue(null as any);

            const res = await request(app)
                .post('/api/users')
                .send({
                    name: 'John Doe',
                    username: 'johndoe',
                    email: '<EMAIL>',
                    password: 'password123',
                    role_id: 1,
                    organization_id: '67e3054d706d97d316aabbe6',
                    token: 'valid-token',
                });
            expect(res.status).toBe(404);
        });

        it('should return 400 due to bad request body', async () => {
            const res = await request(app)
                .post('/api/users')
                .send({
                    name: '',
                    username: '',
                    email: 'john',
                    password: '',
                    role_id: '1',
                    organization_id: '67e3054d706d97d316aabbe6',
                    token: null,
                });
            expect(res.status).toBe(400);
        });

        it('should return 400 if invalid token is provided', async () => {
            jest.spyOn(InviteToken as any, 'findOne').mockResolvedValueOnce(null as any);

            const res = await request(app)
                .post('/api/users')
                .send({
                    name: 'John Doe',
                    username: 'johndoe',
                    email: '<EMAIL>',
                    password: 'password123',
                    role_id: 1,
                    organization_id: '67e3054d706d97d316aabbe6',
                    token: 'invalid-token',
                });
            expect(res.status).toBe(400);
        });

        it('should return 500 if JWT_SECRET is not set', async () => {
            const originalSecret = process.env.JWT_SECRET;
            delete process.env.JWT_SECRET;

            const mockInviteToken = { token: 'mock-token', is_used: false };
            jest.spyOn(InviteToken as any, 'findOne').mockResolvedValueOnce(mockInviteToken);

            const res = await request(app)
                .post('/api/users')
                .send({
                    name: 'John Doe',
                    username: 'johndoe',
                    email: '<EMAIL>',
                    password: 'password123',
                    role_id: 1,
                    organization_id: '67e3054d706d97d316aabbe6',
                    token: 'valid-token',
                });
            expect(res.status).toBe(500);

            process.env.JWT_SECRET = originalSecret;
        });

        it('should create a user and return success message', async () => {
            const mockInviteToken = {
                token: 'mock-token',
                is_used: false,
                invited_by: 'admin123',
                save: jest.fn().mockResolvedValue({} as never)
            };
            jest.spyOn(InviteToken as any, 'findOne').mockResolvedValueOnce(mockInviteToken);
            (jwt.verify as jest.Mock).mockReturnValue({
                email: '<EMAIL>',
                role_id: 1,
                organization_id: '67e3054d706d97d316aabbe6',
                allowed_vessels: null
            });
            jest.spyOn(User as any, 'findOne')
                .mockResolvedValueOnce(null as any)
                .mockResolvedValueOnce(null as any);
            jest.spyOn(Role as any, 'findOne').mockResolvedValue({ role_id: 1 } as any);
            jest.spyOn(Organization as any, 'findOne').mockResolvedValue({ _id: '67e3054d706d97d316aabbe6' } as any);
            jest.spyOn(User as any, 'create').mockResolvedValueOnce({} as any);
            (sendEmail as any).mockResolvedValue('Email sent');

            const res = await request(app)
                .post('/api/users')
                .send({
                    name: 'John Doe',
                    username: 'johndoe',
                    email: '<EMAIL>',
                    password: 'password123',
                    role_id: 1,
                    organization_id: '67e3054d706d97d316aabbe6',
                    token: 'valid-token',
                });
            expect(res.status).toBe(201);
            expect(res.body.message).toBe('User created successfully');
        });

        it('should handle internal server errors', async () => {
            const mockInviteToken = { token: 'mock-token', is_used: false };
            jest.spyOn(InviteToken as any, 'findOne').mockResolvedValueOnce(mockInviteToken);
            (jwt.verify as jest.Mock).mockReturnValue({ email: '<EMAIL>', role_id: 1, organization_id: '67e3054d706d97d316aabbe6' });
            jest.spyOn(User as any, 'findOne').mockRejectedValueOnce(new Error('Something went wrong'));

            const res = await request(app)
                .post('/api/users')
                .send({
                    name: 'John Doe',
                    username: 'johndoe',
                    email: '<EMAIL>',
                    password: 'password123',
                    role_id: 1,
                    organization_id: '67e3054d706d97d316aabbe6',
                    token: 'valid-token',
                });

            expect(res.status).toBe(500);
        });
    });

    describe('PATCH /api/users/:id/role', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                const userId = new mongoose.Types.ObjectId("67e3054d706d97d316aabbe6");
                const roleId = 1;
                const invalidUserId = 'invalidUserId';
                const invalidRoleId = 'invalidRoleId';

                beforeEach(() => {
                    jest.resetAllMocks();
                    (utilsFunctions.validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                    (utilsFunctions.isIntStrict as jest.Mock).mockImplementation((value: any) => {
                        return Number.isInteger(value);
                    });
                });

                if (authMethod === 'api-key') {
                    it('should return 401 if caller does not have required permissions', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey, false);
                        const res = await request(app)
                            .patch(`/api/users/${userId}/role`)
                            .set('Authorization', authToken)
                            .send({ role_id: roleId });
                        expect(res.status).toBe(401);
                    });
                    return;
                }

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch(`/api/users/${userId}/role`).send({ role_id: roleId });
                    expect(res.status).toBe(401);
                });

                it('should return 403 if caller does not have required permissions', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey, false);
                    const res = await request(app)
                        .patch(`/api/users/${userId}/role`)
                        .set('Authorization', nonAuthToken)
                        .send({ role_id: roleId });
                    expect(res.status).toBe(403);
                });

                it('should return 400 if invalid user id is provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .patch(`/api/users/${invalidUserId}/role`)
                        .set('Authorization', authToken)
                        .send({ role_id: roleId });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if invalid role id is provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .patch(`/api/users/${userId}/role`)
                        .set('Authorization', authToken)
                        .send({ role_id: invalidRoleId });
                    expect(res.status).toBe(400);
                });

                it('should return 404 if the user does not exist', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(null as any);

                    const res = await request(app)
                        .patch(`/api/users/${userId}/role`)
                        .set('Authorization', authToken)
                        .send({ role_id: roleId });

                    expect(res.status).toBe(404);
                });

                it('should return 404 if the role does not exist', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce({ _id: userId, role_id: 1, save: (jest.fn() as any).mockResolvedValue({} as any) } as any);
                    jest.spyOn(Role as any, 'findOne').mockResolvedValue(null as any);
                    const res = await request(app)
                        .patch(`/api/users/${userId}/role`)
                        .set('Authorization', authToken)
                        .send({ role_id: 2 });

                    expect(res.status).toBe(404);
                });

                it('should return 403 if the user\'s role is forbidden to be edited', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce({ _id: userId, role_id: 1, save: (jest.fn() as any).mockResolvedValue({} as any) } as any);
                    jest.spyOn(Role as any, 'findOne').mockResolvedValueOnce({ role_id: 2 } as any);

                    const res = await request(app)
                        .patch(`/api/users/${userId}/role`)
                        .set('Authorization', authToken)
                        .send({ role_id: 2 });

                    expect(res.status).toBe(403);
                });

                it('should update the user role and return a success message', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce({ _id: userId, role_id: 2, save: (jest.fn() as any).mockResolvedValue({} as any) } as any);
                    jest.spyOn(Organization as any, 'findOne').mockResolvedValueOnce({
                        is_miscellaneous: false,
                        denied_permissions: []
                    } as any);
                    jest.spyOn(Role as any, 'findOne')
                        .mockResolvedValueOnce({ role_id: 3 } as any)
                        .mockResolvedValueOnce({ role_id: 1, hierarchy_number: 1 } as any)
                        .mockResolvedValueOnce({ role_id: 2, hierarchy_number: 2 } as any);

                    const res = await request(app)
                        .patch(`/api/users/${userId}/role`)
                        .set('Authorization', authToken)
                        .send({ role_id: 3 });

                    expect(res.status).toBe(200);
                });

                it('should return 404 if requesting user role is not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce({ _id: userId, role_id: 2, save: (jest.fn() as any).mockResolvedValue({} as any) } as any);
                    jest.spyOn(Role as any, 'findOne')
                        .mockResolvedValueOnce({ role_id: 3 } as any)
                        .mockResolvedValueOnce(null as any)
                        .mockResolvedValueOnce({ role_id: 2, hierarchy_number: 2 } as any);

                    const res = await request(app)
                        .patch(`/api/users/${userId}/role`)
                        .set('Authorization', authToken)
                        .send({ role_id: 3 });

                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('Role not found');
                });

                it('should return 404 if target user role is not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce({ _id: userId, role_id: 2, save: (jest.fn() as any).mockResolvedValue({} as any) } as any);
                    jest.spyOn(Role as any, 'findOne')
                        .mockResolvedValueOnce({ role_id: 3 } as any)
                        .mockResolvedValueOnce({ role_id: 1, hierarchy_number: 1 } as any)
                        .mockResolvedValueOnce(null as any);

                    const res = await request(app)
                        .patch(`/api/users/${userId}/role`)
                        .set('Authorization', authToken)
                        .send({ role_id: 3 });

                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('Role not found');
                });

                it('should return 403 if requesting user hierarchy is not higher than target user', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce({ _id: userId, role_id: 2, save: (jest.fn() as any).mockResolvedValue({} as any) } as any);
                    jest.spyOn(Role as any, 'findOne')
                        .mockResolvedValueOnce({ role_id: 3 } as any)
                        .mockResolvedValueOnce({ role_id: 1, hierarchy_number: 2 } as any)
                        .mockResolvedValueOnce({ role_id: 2, hierarchy_number: 2 } as any);

                    const res = await request(app)
                        .patch(`/api/users/${userId}/role`)
                        .set('Authorization', authToken)
                        .send({ role_id: 3 });

                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe('Not allowed to edit role for user having role above you');
                });

                it('should return 403 if non-internal user tries to edit user from different organization', async () => {
                    const nonInternalUser = {
                        ...userOrApiKey.authorized,
                        organization: { is_internal: false },
                        organization_id: 'different-org-id'
                    };
                    (jwt.verify as jest.Mock).mockReturnValue({ user_id: userOrApiKey.authorized._id });
                    (getUser as jest.Mock).mockResolvedValue(nonInternalUser as never);

                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce({
                        _id: userId,
                        role_id: 2,
                        organization_id: 'another-org-id',
                        save: (jest.fn() as any).mockResolvedValue({} as any)
                    } as any);
                    jest.spyOn(Role as any, 'findOne')
                        .mockResolvedValueOnce({ role_id: 3 } as any)
                        .mockResolvedValueOnce({ role_id: 1, hierarchy_number: 1 } as any)
                        .mockResolvedValueOnce({ role_id: 2, hierarchy_number: 2 } as any);
                    jest.spyOn(Organization as any, 'findOne').mockResolvedValueOnce({
                        is_miscellaneous: false,
                        denied_permissions: []
                    } as any);

                    const res = await request(app)
                        .patch(`/api/users/${userId}/role`)
                        .set('Authorization', authToken)
                        .send({ role_id: 3 });

                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe('Not allowed to edit role of user from different organization');
                });

                it('should return 404 if user organization does not exist', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce({ _id: userId, role_id: 2, organization_id: 'non-existent-org', save: (jest.fn() as any).mockResolvedValue({} as any) } as any);
                    jest.spyOn(Role as any, 'findOne')
                        .mockResolvedValueOnce({ role_id: 3 } as any)
                        .mockResolvedValueOnce({ role_id: 1, hierarchy_number: 1 } as any)
                        .mockResolvedValueOnce({ role_id: 2, hierarchy_number: 2 } as any);
                    jest.spyOn(Organization as any, 'findOne').mockResolvedValueOnce(null as any);

                    const res = await request(app)
                        .patch(`/api/users/${userId}/role`)
                        .set('Authorization', authToken)
                        .send({ role_id: 3 });

                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('User organization does not exist');
                });

                it('should return 403 if miscellaneous organization user tries to assign role with manage user permission', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce({ _id: userId, role_id: 2, organization_id: 'misc-org', save: (jest.fn() as any).mockResolvedValue({} as any) } as any);
                    jest.spyOn(Role as any, 'findOne')
                        .mockResolvedValueOnce({ role_id: 3, denied_permissions: [] } as any)
                        .mockResolvedValueOnce({ role_id: 1, hierarchy_number: 1 } as any)
                        .mockResolvedValueOnce({ role_id: 2, hierarchy_number: 2 } as any);
                    jest.spyOn(Organization as any, 'findOne').mockResolvedValueOnce({
                        is_miscellaneous: true,
                        denied_permissions: []
                    } as any);

                    const res = await request(app)
                        .patch(`/api/users/${userId}/role`)
                        .set('Authorization', authToken)
                        .send({ role_id: 3 });

                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe('Miscellaneous organization user are not allowed to assign a role which have manage user permission');
                });

                it('should handle internal server errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockRejectedValueOnce(new Error('Something went wrong'));

                    const res = await request(app)
                        .patch(`/api/users/${userId}/role`)
                        .set('Authorization', authToken)
                        .send({ role_id: roleId });

                    expect(res.status).toBe(500);
                });
            });

        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('DELETE /api/users/:id', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                const userId = new mongoose.Types.ObjectId().toString();
                const invalidUserId = 'invalidUserId';

                beforeEach(() => {
                    jest.resetAllMocks();
                    (utilsFunctions.validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).delete(`/api/users/${userId}`);
                    expect(res.status).toBe(401);
                });

                it('should return 403 if caller does not have required permissions', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey, false);
                    const res = await request(app)
                        .delete(`/api/users/${userId}`)
                        .set('Authorization', nonAuthToken);
                    expect(res.status).toBe(403);
                });

                it('should return 400 if invalid user id is provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .delete(`/api/users/${invalidUserId}`)
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 404 if the user does not exist', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findById').mockResolvedValueOnce(null as any);

                    const res = await request(app)
                        .delete(`/api/users/${userId}`)
                        .set('Authorization', authToken);

                    expect(res.status).toBe(404);
                });

                it('should return 400 if the user cannot be deleted', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findById').mockResolvedValueOnce({ _id: userId, deletable: false } as any);

                    const res = await request(app)
                        .delete(`/api/users/${userId}`)
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should delete the user and return a success message', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findById').mockResolvedValueOnce({
                        _id: userId,
                        deletable: true,
                        save: (jest.fn() as any).mockResolvedValue({} as any),
                    } as any);

                    const res = await request(app)
                        .delete(`/api/users/${userId}`)
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                });

                it('should handle internal server errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findById').mockRejectedValueOnce(new Error('Something went wrong'));

                    const res = await request(app)
                        .delete(`/api/users/${userId}`)
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/users/forgot-password', () => {

        const validEmail = '<EMAIL>';
        const invalidEmail = 'invalid-email';

        beforeEach(() => {
            jest.resetAllMocks();
            (utilsFunctions.validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
            });
        });

        it('should return 400 if email is not provided', async () => {
            const res = await request(app).post('/api/users/forgot-password').send({});
            expect(res.status).toBe(400);
        });

        it('should return 400 if an invalid email format is provided', async () => {
            const res = await request(app).post('/api/users/forgot-password').send({ email: invalidEmail });
            expect(res.status).toBe(400);
        });

        it('should return 404 if no user is found with the provided email', async () => {
            jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(null as any);

            const res = await request(app).post('/api/users/forgot-password').send({ email: validEmail });
            expect(res.status).toBe(404);
        });

        it('should return 400 if a reset token already exists and is still valid', async () => {
            jest.spyOn(User as any, 'findOne').mockResolvedValueOnce({
                email: validEmail,
                reset_password_token: 'someToken',
                reset_password_expire: Date.now() + 5 * 60 * 1000
            } as any);

            const res = await request(app).post('/api/users/forgot-password').send({ email: validEmail });
            expect(res.status).toBe(400);
        });

        it('should generate a reset token and send an email', async () => {
            const resetToken = 'generatedResetToken';
            const user = {
                email: validEmail,
                reset_password_token: null,
                reset_password_expire: null,
                save: (jest.fn() as any).mockResolvedValue({} as any)
            };

            jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(user as any);
            jest.spyOn(crypto as any, 'randomBytes').mockReturnValueOnce(Buffer.from(resetToken, 'hex'));
            jest.spyOn(crypto as any, 'createHash').mockReturnValueOnce({
                update: jest.fn().mockReturnValueOnce({ digest: jest.fn().mockReturnValueOnce('hashedToken') })
            } as any);
            (sendEmail as any).mockImplementation = (jest.fn() as any).mockResolvedValue('Email sent');

            const res = await request(app).post('/api/users/forgot-password').send({ email: validEmail });

            expect(res.status).toBe(200);
            expect(user.reset_password_token).toBe('hashedToken');
            expect(user.reset_password_expire).toBeGreaterThan(Date.now());
        });

        it('should handle internal server errors', async () => {
            jest.spyOn(User as any, 'findOne').mockRejectedValueOnce(new Error('Something went wrong'));

            const res = await request(app).post('/api/users/forgot-password').send({ email: validEmail });
            expect(res.status).toBe(500);
        });
    });

    describe('POST /api/users/reset-password/:token', () => {
        const mockUser = {
            reset_password_token: 'hashed_token',
            reset_password_expire: Date.now() + 10000,
        };
        const token = 'ea2b081d1a2dce127ca87543c656a062f51504c9fa9f9a353ba8d9122e8a3ca7';
        const validPassword = 'validpassword';
        const shortPassword = 'short';

        beforeEach(() => {
            jest.resetAllMocks();
            (utilsFunctions.validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
            });
        });

        it('should return 400 if invalid request body is provided', async () => {
            const res = await request(app)
                .post(`/api/users/reset-password/invalidToken`)
                .send({ password: '' });

            expect(res.status).toBe(400);
        });

        it('should return 400 if the token is invalid or expired', async () => {
            jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(null as any);

            const res = await request(app)
                .post(`/api/users/reset-password/${token}`)
                .send({ password: validPassword });

            expect(res.status).toBe(400);
            expect(res.body.message).toBe('Invalid or expired token');
        });

        it('should return 400 if the password is less than 8 characters', async () => {
            jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(mockUser as any);

            const res = await request(app)
                .post(`/api/users/reset-password/${token}`)
                .send({ password: shortPassword });

            expect(res.status).toBe(400);
        });

        it('should reset the password successfully', async () => {
            jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(mockUser as any);
            (mockUser as any).save = (jest.fn() as any).mockResolvedValue({} as any);

            const res = await request(app)
                .post(`/api/users/reset-password/${token}`)
                .send({ password: validPassword });

            expect(res.status).toBe(200);
            expect(res.body.message).toBe('Password has been reset successfully');
        });

        it('should handle internal server errors', async () => {
            jest.spyOn(User as any, 'findOne').mockRejectedValueOnce(new Error('Database error'));

            const res = await request(app)
                .post(`/api/users/reset-password/${token}`)
                .send({ password: validPassword });

            expect(res.status).toBe(500);
        });
    });

    describe('PATCH /api/users/userEmailVerification', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (utilsFunctions.validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch('/api/users/userEmailVerification');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if email_verification_enabled is not a boolean', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .patch('/api/users/userEmailVerification')
                        .set('Authorization', authToken)
                        .query({ email_verification_enabled: 'invalid-value' });
                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe("Invalid value");
                });

                it('should return 400 if email_verification_enabled is empty string', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .patch('/api/users/userEmailVerification')
                        .set('Authorization', authToken)
                        .query({ email_verification_enabled: '' });
                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe("Invalid value");
                });

                it('should return 404 if user is not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findById').mockResolvedValueOnce(null as any);

                    const res = await request(app)
                        .patch('/api/users/userEmailVerification')
                        .set('Authorization', authToken)
                        .query({ email_verification_enabled: 'true' });

                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('User does not exist');
                });

                it('should return 200 and update the email_verification_enabled status successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const mockUpdatedUser = {
                        _id: 'user-id',
                        email: '<EMAIL>',
                        email_verification_enabled: true,
                        save: jest.fn()
                    };

                    jest.spyOn(User as any, 'findById').mockResolvedValueOnce(mockUpdatedUser as any);

                    const res = await request(app)
                        .patch('/api/users/userEmailVerification')
                        .set('Authorization', authToken)
                        .query({ email_verification_enabled: 'true' });

                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('User email verification status updated successfully');
                });

                it('should return 400 and if email_verification_enabled is true and user dont have an email', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const mockUpdatedUser = {
                        _id: 'user-id',
                        email: '',
                        email_verification_enabled: true,
                        save: jest.fn()
                    };

                    jest.spyOn(User as any, 'findById').mockResolvedValueOnce(mockUpdatedUser as any);

                    const res = await request(app)
                        .patch('/api/users/userEmailVerification')
                        .set('Authorization', authToken)
                        .query({ email_verification_enabled: 'true' });

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('Email is required');
                });

                it('should return 500 if an error occurs during the update', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findById').mockRejectedValueOnce(new Error('Database error'));

                    const res = await request(app)
                        .patch('/api/users/userEmailVerification')
                        .set('Authorization', authToken)
                        .query({ email_verification_enabled: 'true' });

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/users/emailOTPVerification', () => {
        beforeEach(() => {
            jest.resetAllMocks();
            (utilsFunctions.validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
            });
            (otpService.verifyOtp as jest.Mock).mockImplementation((_value: any) => {
                return { valid: true, message: 'OTP verified successfully' };
            });
        });

        it('should return 401 if no token is provided', async () => {
            const res = await request(app).post('/api/users/emailOTPVerification').send({ username: 'testuser', otp: 123456 });
            expect(res.status).toBe(400);
        });

        it('should return 400 if username is not provided', async () => {
            const res = await request(app)
                .post('/api/users/emailOTPVerification')
                .send({ otp: 123456 });
            expect(res.status).toBe(400);
            expect(res.body.message).toBe('Username is required');
        });

        it('should return 400 if username is empty string', async () => {
            const res = await request(app)
                .post('/api/users/emailOTPVerification')
                .send({ username: '', otp: 123456 });
            expect(res.status).toBe(400);
            expect(res.body.message).toBe("Invalid value '' provided for field 'username'");
        });

        it('should return 400 if otp is empty string', async () => {
            const res = await request(app)
                .post('/api/users/emailOTPVerification')
                .send({ username: 'testuser', otp: null });
            expect(res.status).toBe(400);
            expect(res.body.message).toBe("Field 'otp' cannot be empty");
        });

        it('should return 400 if otp is not a valid 6-digit integer', async () => {
            const res = await request(app)
                .post('/api/users/emailOTPVerification')
                .send({ username: 'testuser', otp: 'invalid' });
            expect(res.status).toBe(400);
            expect(res.body.message).toBe("Field 'otp' must be a 6-digit integer");
        });

        it('should return 400 if user is not found', async () => {
            jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(null as any);
            const res = await request(app)
                .post('/api/users/emailOTPVerification')
                .send({ username: 'nonexistentuser', otp: 123456 });
            expect(res.status).toBe(400);
            expect(res.body.message).toBe('Invalid credentials');
        });

        it('should return 400 if email is not available for the user', async () => {
            const mockUser = { username: 'testuser', email: null };
            jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(mockUser as any);
            const res = await request(app)
                .post('/api/users/emailOTPVerification')
                .send({ username: 'testuser', otp: 123456 });
            expect(res.status).toBe(400);
            expect(res.body.message).toBe('Email is required');
        });

        it('should return 400 if OTP is invalid', async () => {
            const mockUser = { email: '<EMAIL>' };
            jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(mockUser as any);
            (otpService.verifyOtp as jest.Mock).mockImplementation((_value: any) => {
                return { valid: false, message: 'Invalid OTP' };
            });
            const res = await request(app)
                .post('/api/users/emailOTPVerification')
                .send({ username: 'testuser', otp: 654321 });
            expect(res.status).toBe(400);
        });

        it('should return 200 if OTP is verified successfully', async () => {
            const mockUser = {
                email: '<EMAIL>',
                email_verified_device_ids: [],
                save: jest.fn()
            };
            otpService.otpStore.push({
                email: mockUser.email,
                otp: 123456,
                expiresAt: Date.now() + (1000 * 60)
            });

            jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(mockUser as any);

            const res = await request(app)
                .post('/api/users/emailOTPVerification')
                .set('Cookie', 'deviceId=device12345')
                .send({ username: 'testuser', otp: 123456 });

            expect(res.status).toBe(200);
            expect(mockUser.save).toHaveBeenCalled();
        });

        it('should not update or save user if deviceId already exists', async () => {
            const mockUser = {
                email: '<EMAIL>',
                email_verified_device_ids: ['device12345'],
                save: jest.fn()
            };
            otpService.otpStore.push({
                email: mockUser.email,
                otp: 123456,
                expiresAt: Date.now() + (1000 * 60)
            });

            jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(mockUser as any);

            const res = await request(app)
                .post('/api/users/emailOTPVerification')
                .set('Cookie', 'deviceId=device12345')
                .send({ username: 'testuser', otp: 123456 });

            expect(res.status).toBe(200);
            expect(mockUser.save).not.toHaveBeenCalled();
        });
    });

    describe('POST /api/users/sendEmailOTP', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (utilsFunctions.validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                    (otpService.sendOtp as jest.Mock).mockImplementation((_value: any) => {
                        return { message: 'OTP sent successfully' };
                    });
                });

                it('should return 400 if username is not provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .post('/api/users/sendEmailOTP')
                        .set('Authorization', authToken)
                        .send({});

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('Username is required');
                });

                it('should return 400 if username is empty string', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .post('/api/users/sendEmailOTP')
                        .set('Authorization', authToken)
                        .send({ username: '' });

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe("Invalid value '' provided for field 'username'");
                });

                it('should return 400 if user with provided username is not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const mockUser = null;
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(mockUser as any);

                    const res = await request(app)
                        .post('/api/users/sendEmailOTP')
                        .set('Authorization', authToken)
                        .send({ username: 'nonexistentuser' });

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('Invalid credentials');
                });

                it('should return 400 if user does not have an email', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const mockUser = { username: 'testuser', email: '' };
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(mockUser as any);

                    const res = await request(app)
                        .post('/api/users/sendEmailOTP')
                        .set('Authorization', authToken)
                        .send({ username: 'testuser' });

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('Email is required');
                });

                it('should return 200 and send OTP successfully if email is found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const mockUser = { username: 'testuser', email: '<EMAIL>' };
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(mockUser as any);

                    const mockSendOtpResponse = { message: 'OTP sent successfully' };
                    jest.spyOn(otpService, 'sendOtp').mockResolvedValueOnce(mockSendOtpResponse);

                    const res = await request(app)
                        .post('/api/users/sendEmailOTP')
                        .set('Authorization', authToken)
                        .send({ username: 'testuser' });

                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(mockSendOtpResponse);
                });

                it('should return 500 and throw error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const mockUser = { username: 'testuser', email: '<EMAIL>' };
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(mockUser as any);

                    const mockError = new Error('Something went wrong');

                    (otpService.sendOtp as jest.Mock).mockRejectedValueOnce(mockError as never);
                    const res = await request(app)
                        .post('/api/users/sendEmailOTP')
                        .set('Authorization', authToken)
                        .send({ username: 'testuser' });

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/users/invite', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                    (utilsFunctions.validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                    (utilsFunctions.generateInvitationLink as jest.Mock).mockImplementation((_value: any) => {
                        return { message: 'Invitation link generated successfully' };
                    });
                    (sendEmail as jest.Mock).mockImplementation((_value: any) => {
                        return { message: 'Email sent successfully' };
                    });
                    (utilsFunctions.isIntStrict as jest.Mock).mockImplementation((value: any) => {
                        return Number.isInteger(value);
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/users/invite');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if role_id is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .post('/api/users/invite')
                        .set('Authorization', authToken)
                        .send({ email: '<EMAIL>' });
                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe("Invalid value 'undefined' provided for field 'role_id'");
                });

                it('should return 400 if email is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .post('/api/users/invite')
                        .set('Authorization', authToken)
                        .send({ email: 'invalidEmail' });
                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe("Invalid value 'invalidEmail' provided for field 'email'");
                });

                it('should return 400 if allowed_vessels is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .post('/api/users/invite')
                        .set('Authorization', authToken)
                        .send({ allowed_vessels: ["invalidVessel"] });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if role_id is not an integer', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .post('/api/users/invite')
                        .set('Authorization', authToken)
                        .send({ email: '<EMAIL>', role_id: 'not-a-number' });
                    expect(res.status).toBe(400);
                    expect(res.body.message).toContain('Invalid value');
                });

                it('should return 400 if user with the given email already exists', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockResolvedValue({ _id: new (mongoose as any).Types.ObjectId(), email: '<EMAIL>' } as any);

                    const res = await request(app)
                        .post('/api/users/invite')
                        .set('Authorization', authToken)
                        .send({ email: '<EMAIL>', role_id: 1, allowed_vessels: ['665432109876543210987654', "665432109876543210987654"], organization_id: "665432109876543210987654" });

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('User already exists with this email');
                });

                it('should return 400 if role does not exist', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockResolvedValue(null as any);
                    jest.spyOn(Role as any, 'findOne').mockResolvedValue(null as any);

                    const res = await request(app)
                        .post('/api/users/invite')
                        .set('Authorization', authToken)
                        .send({ email: '<EMAIL>', role_id: 999, allowed_vessels: ['665432109876543210987654', "665432109876543210987654"], organization_id: "665432109876543210987654" });

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('Role does not exist with this ID');
                });

                it('should successfully send an invitation email', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(Role as any, 'findOne').mockResolvedValue({ role_id: 1, role_name: 'Admin', denied_permissions: [] } as any);
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(null as any);
                    jest.spyOn(Organization as any, 'findOne').mockResolvedValueOnce({
                        _id: "665432109876543210987654",
                        name: "Test Organization",
                        is_miscellaneous: false
                    } as any);
                    (InviteToken as unknown as jest.Mock).mockImplementation((data: any) => ({
                        ...data,
                        save: jest.fn().mockResolvedValue({
                            _id: "mocked_id",
                            ...data,
                        } as never),
                    }));

                    const mockLink = 'http://example.com/invite-link';
                    jest.spyOn(utilsFunctions, 'generateInvitationLink').mockReturnValue(mockLink);
                    (sendEmail as any).mockResolvedValue(true);

                    const res = await request(app)
                        .post('/api/users/invite')
                        .set('Authorization', authToken)
                        .send({ email: '<EMAIL>', role_id: 1, allowed_vessels: ['665432109876543210987654', "665432109876543210987654"], organization_id: "665432109876543210987654" });

                    expect(res.status).toBe(200);
                    expect(sendEmail).toHaveBeenCalled();
                    expect(res.body).toHaveProperty("link");
                });

                it('should return 400 if organization does not exist', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(null as any);
                    jest.spyOn(Role as any, 'findOne').mockResolvedValueOnce({ role_id: 1, role_name: 'Admin', denied_permissions: [] } as any);
                    jest.spyOn(Organization as any, 'findOne').mockResolvedValueOnce(null as any);

                    const res = await request(app)
                        .post('/api/users/invite')
                        .set('Authorization', authToken)
                        .send({ email: '<EMAIL>', role_id: 1, allowed_vessels: ['665432109876543210987654'], organization_id: '665432109876543210987654' });

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('Organization does not exist with this ID');
                });

                it('should return 403 if inviting to different org without manage org permission', async () => {
                    const requester = {
                        ...userOrApiKey.authorized,
                        organization_id: '111111111111111111111111',
                        permissions: userOrApiKey.authorized.permissions.filter((p: any) => p.permission_id !== 1000),
                    } as any;
                    setupAuthorizedAuthMocks(authMethod, { authorized: requester }, authToken, User, ApiKey, false);

                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(null as any);
                    jest.spyOn(Role as any, 'findOne').mockResolvedValueOnce({ role_id: 1, role_name: 'Admin', denied_permissions: [] } as any);
                    jest.spyOn(Organization as any, 'findOne').mockResolvedValueOnce({ _id: '222222222222222222222222', name: 'X', is_miscellaneous: false } as any);

                    const res = await request(app)
                        .post('/api/users/invite')
                        .set('Authorization', authToken)
                        .send({ email: '<EMAIL>', role_id: 1, allowed_vessels: ['665432109876543210987654'], organization_id: '222222222222222222222222' });

                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe('Forbidden: You can only invite users to your own organization');
                });

                it('should return 403 for miscellaneous organization when role allows manage users', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(null as any);
                    jest.spyOn(Role as any, 'findOne').mockResolvedValueOnce({ role_id: 1, role_name: 'Admin', denied_permissions: [] } as any);
                    jest.spyOn(Organization as any, 'findOne').mockResolvedValueOnce({ _id: '665432109876543210987654', name: 'Misc Org', is_miscellaneous: true } as any);

                    const res = await request(app)
                        .post('/api/users/invite')
                        .set('Authorization', authToken)
                        .send({ email: '<EMAIL>', role_id: 1, allowed_vessels: ['665432109876543210987654'], organization_id: '665432109876543210987654' });

                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe('Miscellaneous organization user are not allowed to assign a role which have manage user permission');
                });

                it('should handle internal server errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockRejectedValueOnce(new Error('Something went wrong'));

                    const res = await request(app)
                        .post('/api/users/invite')
                        .set('Authorization', authToken)
                        .send({ email: '<EMAIL>', role_id: 1, allowed_vessels: ['665432109876543210987654', "665432109876543210987654"], organization_id: "665432109876543210987654" });

                    expect(res.status).toBe(authMethod === 'user' ? 500 : 403);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
    });

    describe('POST /api/users/verify-invite', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (utilsFunctions.validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                });

                it('should return 400 if invite token is missing', async () => {
                    const res = await request(app)
                        .get('/api/users/verify-invite')
                        .set('Authorization', authToken)
                    expect(res.status).toBe(400);
                });

                it('should return 302 and redirect', async () => {
                    jest.spyOn(InviteToken as any, 'findOne').mockResolvedValueOnce({ short_token: 'valid-token', is_used: false, is_deleted: false, token: 'jwt-token' } as any);
                    jest.spyOn(jwt, 'verify').mockImplementation(() => ({ email: 'e', role_id: 1, role: 'Admin', organization_id: 'org', organization_name: 'Org' } as any));
                    const res = await request(app)
                        .get('/api/users/verify-invite')
                        .set('Authorization', authToken)
                        .query({ token: 'valid-token' });
                    expect(res.status).toBe(302);
                });

                it('should return 400 if invite token is invalid', async () => {
                    jest.spyOn(InviteToken as any, 'findOne').mockResolvedValueOnce(null as any);
                    jest.spyOn(jwt, 'verify').mockImplementation(() => ({ email: 'e', role_id: 1, role: 'Admin', organization_id: 'org', organization_name: 'Org' } as any));
                    const res = await request(app)
                        .get('/api/users/verify-invite')
                        .set('Authorization', authToken)
                        .query({ token: 'valid-token' });
                    expect(res.status).toBe(400);
                });

                it('should handle internal server errors', async () => {
                    jest.spyOn(InviteToken as any, 'findOne').mockResolvedValueOnce({ short_token: 'valid-token', is_used: false, is_deleted: false, token: 'jwt-token' } as any);
                    const originalSecret = process.env.JWT_SECRET;
                    delete process.env.JWT_SECRET;
                    const res = await request(app)
                        .get('/api/users/verify-invite')
                        .query({ token: 'valid-token' });

                    expect(res.status).toBe(500);
                    process.env.JWT_SECRET = originalSecret;
                });

            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('PATCH /api/users/:id/allowedVessels', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                const userId = new mongoose.Types.ObjectId().toString();
                const invalidUserId = 'invalidUserId';

                beforeEach(() => {
                    jest.resetAllMocks();
                    (utilsFunctions.validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                });

                if (authMethod === 'api-key') {
                    it('should return 401 if caller does not have required permissions', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey, false);
                        const res = await request(app)
                            .patch(`/api/users/${userId}/allowedVessels`)
                            .send({ allowed_vessels: [new mongoose.Types.ObjectId().toString()] });
                        expect(res.status).toBe(401);
                    });
                    return;
                }

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app)
                        .patch(`/api/users/${userId}/allowedVessels`)
                        .send({ allowed_vessels: [new mongoose.Types.ObjectId().toString()] });
                    expect(res.status).toBe(401);
                });

                it('should return 400 if invalid user id is provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .patch(`/api/users/${invalidUserId}/allowedVessels`)
                        .set('Authorization', authToken)
                        .send({ allowed_vessels: [new mongoose.Types.ObjectId().toString()] });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if allowed_vessels contains invalid ids', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .patch(`/api/users/${userId}/allowedVessels`)
                        .set('Authorization', authToken)
                        .send({ allowed_vessels: ['invalid-id'] });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if allowed_vessels is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .patch(`/api/users/${userId}/allowedVessels`)
                        .set('Authorization', authToken)
                        .send({ allowed_vessels: null });
                    expect(res.status).toBe(400);
                });

                it('should return 404 if the user does not exist', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(null as any);
                    const res = await request(app)
                        .patch(`/api/users/${userId}/allowedVessels`)
                        .set('Authorization', authToken)
                        .send({ allowed_vessels: [new mongoose.Types.ObjectId().toString()] });
                    expect(res.status).toBe(404);
                });

                it('should return 400 if any vessel is inactive', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce({ _id: userId, save: (jest.fn() as any).mockResolvedValue({} as any) } as any);
                    const v1 = new mongoose.Types.ObjectId().toString();
                    jest.spyOn(Vessel as any, 'find').mockResolvedValueOnce([{ _id: v1, is_active: false }]);
                    const res = await request(app)
                        .patch(`/api/users/${userId}/allowedVessels`)
                        .set('Authorization', authToken)
                        .send({ allowed_vessels: [v1] });
                    expect(res.status).toBe(400);
                });

                it('should update allowed vessels successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const saveMock = (jest.fn() as any).mockResolvedValue({} as any);
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce({ _id: userId, allowed_vessels: [], save: saveMock } as any);
                    jest.spyOn(Vessel as any, 'find').mockResolvedValueOnce([]);
                    const v1 = new mongoose.Types.ObjectId().toString();
                    const res = await request(app)
                        .patch(`/api/users/${userId}/allowedVessels`)
                        .set('Authorization', authToken)
                        .send({ allowed_vessels: [v1] });
                    expect(res.status).toBe(200);
                });

                it('should handle internal server errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockRejectedValueOnce(new Error('Something went wrong'));
                    const res = await request(app)
                        .patch(`/api/users/${userId}/allowedVessels`)
                        .set('Authorization', authToken)
                        .send({ allowed_vessels: [new mongoose.Types.ObjectId().toString()] });
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('PATCH /api/users/:id/organization', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                const userId = new mongoose.Types.ObjectId().toString();
                const invalidUserId = 'invalidUserId';
                const invalidOrgId = 'invalidOrgId';

                beforeEach(() => {
                    jest.resetAllMocks();
                    (utilsFunctions.validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                });

                if (authMethod === 'api-key') {
                    it('should return 401 if caller does not have required permissions', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey, false);
                        const res = await request(app)
                            .patch(`/api/users/${userId}/organization`)
                            .set('Authorization', authToken)
                            .send({ organization_id: new mongoose.Types.ObjectId().toString() });
                        expect(res.status).toBe(401);
                    });
                    return;
                }

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app)
                        .patch(`/api/users/${userId}/organization`)
                        .send({ organization_id: new mongoose.Types.ObjectId().toString() });
                    expect(res.status).toBe(401);
                });

                it('should return 400 if invalid user id is provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .patch(`/api/users/${invalidUserId}/organization`)
                        .set('Authorization', authToken)
                        .send({ organization_id: new mongoose.Types.ObjectId().toString() });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if invalid organization id is provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .patch(`/api/users/${userId}/organization`)
                        .set('Authorization', authToken)
                        .send({ organization_id: invalidOrgId });
                    expect(res.status).toBe(400);
                });

                it('should return 404 if user does not exist', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce(null as any);
                    const res = await request(app)
                        .patch(`/api/users/${userId}/organization`)
                        .set('Authorization', authToken)
                        .send({ organization_id: new mongoose.Types.ObjectId().toString() });
                    expect(res.status).toBe(404);
                });

                it('should return 404 if organization does not exist', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce({ _id: userId, role_id: 2 } as any);
                    jest.spyOn(Role as any, 'findOne').mockResolvedValueOnce({ role_id: 2, hierarchy_number: 5 } as any);
                    jest.spyOn(Role as any, 'findOne').mockResolvedValueOnce({ role_id: 1, hierarchy_number: 1 } as any);
                    jest.spyOn(Organization as any, 'findOne').mockResolvedValueOnce(null as any);
                    const res = await request(app)
                        .patch(`/api/users/${userId}/organization`)
                        .set('Authorization', authToken)
                        .send({ organization_id: new mongoose.Types.ObjectId().toString() });
                    expect(res.status).toBe(404);
                });

                it('should return 403 if trying to edit own organization', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce({ _id: userOrApiKey.authorized._id, role_id: 2 } as any);
                    jest.spyOn(Role as any, 'findOne').mockResolvedValueOnce({ role_id: 2, hierarchy_number: 5 } as any);
                    jest.spyOn(Role as any, 'findOne').mockResolvedValueOnce({ role_id: 1, hierarchy_number: 1 } as any);
                    jest.spyOn(Organization as any, 'findOne').mockResolvedValueOnce({ _id: new mongoose.Types.ObjectId().toString(), is_miscellaneous: false } as any);
                    const res = await request(app)
                        .patch(`/api/users/${userOrApiKey.authorized._id}/organization`)
                        .set('Authorization', authToken)
                        .send({ organization_id: new mongoose.Types.ObjectId().toString() });
                    expect(res.status).toBe(403);
                });

                it('should return 404 if any role is not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce({ _id: userId, role_id: 2 } as any);
                    jest.spyOn(Role as any, 'findOne')
                        .mockResolvedValueOnce(null as any)
                        .mockResolvedValueOnce({ role_id: 1, hierarchy_number: 1 } as any);
                    jest.spyOn(Organization as any, 'findOne').mockResolvedValueOnce({
                        is_miscellaneous: false,
                        _id: new mongoose.Types.ObjectId().toString()
                    } as any);
                    const res = await request(app)
                        .patch(`/api/users/${userId}/organization`)
                        .set('Authorization', authToken)
                        .send({ organization_id: new mongoose.Types.ObjectId().toString() });
                    expect(res.status).toBe(404);
                });

                it('should return 403 if organization is miscellaneous and requester does not have manage users permission', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce({ _id: userId, role_id: 2 } as any);
                    jest.spyOn(Role as any, 'findOne')
                        .mockResolvedValueOnce({ role_id: 2, hierarchy_number: 2, denied_permissions: [] } as any)
                        .mockResolvedValueOnce({ role_id: 1, hierarchy_number: 1, denied_permissions: [] } as any);
                    jest.spyOn(Organization as any, 'findOne').mockResolvedValueOnce({ _id: new mongoose.Types.ObjectId().toString(), is_miscellaneous: true } as any);
                    const res = await request(app)
                        .patch(`/api/users/${userId}/organization`)
                        .set('Authorization', authToken)
                        .send({ organization_id: new mongoose.Types.ObjectId().toString() });
                    expect(res.status).toBe(403);
                });

                it('should return 403 if requester hierarchy is not higher than target user', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce({ _id: userId, role_id: 2 } as any);
                    jest.spyOn(Role as any, 'findOne')
                        .mockResolvedValueOnce({ role_id: 2, hierarchy_number: 2 } as any)
                        .mockResolvedValueOnce({ role_id: 1, hierarchy_number: 2 } as any);
                    jest.spyOn(Organization as any, 'findOne').mockResolvedValueOnce({ _id: new mongoose.Types.ObjectId().toString(), is_miscellaneous: false } as any);
                    const res = await request(app)
                        .patch(`/api/users/${userId}/organization`)
                        .set('Authorization', authToken)
                        .send({ organization_id: new mongoose.Types.ObjectId().toString() });
                    expect(res.status).toBe(403);
                });

                it('should update organization successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const saveMock = (jest.fn() as any).mockResolvedValue({} as any);
                    jest.spyOn(User as any, 'findOne').mockResolvedValueOnce({ _id: userId, role_id: 2, organization_id: 'old', save: saveMock } as any);
                    jest.spyOn(Role as any, 'findOne')
                        .mockResolvedValueOnce({ role_id: 2, hierarchy_number: 5 } as any)
                        .mockResolvedValueOnce({ role_id: 1, hierarchy_number: 1 } as any);
                    jest.spyOn(Organization as any, 'findOne').mockResolvedValueOnce({ _id: new mongoose.Types.ObjectId().toString(), is_miscellaneous: false } as any);
                    const res = await request(app)
                        .patch(`/api/users/${userId}/organization`)
                        .set('Authorization', authToken)
                        .send({ organization_id: new mongoose.Types.ObjectId().toString() });
                    expect(res.status).toBe(200);
                });

                it('should handle internal server errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findOne').mockRejectedValueOnce(new Error('Something went wrong'));
                    const res = await request(app)
                        .patch(`/api/users/${userId}/organization`)
                        .set('Authorization', authToken)
                        .send({ organization_id: new mongoose.Types.ObjectId().toString() });
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('PATCH /api/users/updateSettings', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (utilsFunctions.validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error occured: ' + err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch('/api/users/updateSettings');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if no setting is provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .patch('/api/users/updateSettings')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it("should return 400 with validator message for invalid 'use_MGRS'", async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .patch('/api/users/updateSettings?use_MGRS=notabool')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe("Invalid value 'notabool' provided for field 'use_MGRS'");
                });

                it("should return 400 with validator message for invalid 'date_time_format' type", async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .patch('/api/users/updateSettings?date_time_format[]=LOCAL')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe("Invalid value 'LOCAL' provided for field 'date_time_format'");
                });

                it("should return 400 with validator message for invalid 'home_port_filter_mode' type", async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const res = await request(app)
                        .patch('/api/users/updateSettings?home_port_filter_mode[]=ALL')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe("Invalid value 'ALL' provided for field 'home_port_filter_mode'");
                });

                it('should return 404 if user does not exist', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findById').mockResolvedValueOnce(null as any);
                    const res = await request(app)
                        .patch('/api/users/updateSettings?date_time_format=UTC')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(404);
                });

                it('should update provided settings successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    const saveMock = (jest.fn() as any).mockResolvedValue({} as any);
                    jest.spyOn(User as any, 'findById').mockResolvedValueOnce({ _id: userOrApiKey.authorized._id, save: saveMock } as any);
                    const res = await request(app)
                        .patch('/api/users/updateSettings?date_time_format=UTC&use_MGRS=true&home_port_filter_mode=ALL')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should handle internal server errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey, false);
                    jest.spyOn(User as any, 'findById').mockRejectedValueOnce(new Error('Something went wrong'));
                    const res = await request(app)
                        .patch('/api/users/updateSettings?date_time_format=UTC')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});
