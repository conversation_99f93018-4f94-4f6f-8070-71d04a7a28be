import { jest, describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { Buffer } from 'node:buffer';

const originalEnv = process.env;

const mockKinesisVideo = {
    listStreams: jest.fn() as jest.MockedFunction<any>,
    listTagsForStream: jest.fn() as jest.MockedFunction<any>,
    getDataEndpoint: jest.fn() as jest.MockedFunction<any>,
} as any;

const mockKinesisVideoArchivedMedia = {
    getHLSStreamingSessionURL: jest.fn() as jest.MockedFunction<any>,
    getDASHStreamingSessionURL: jest.fn() as jest.MockedFunction<any>,
    getImages: jest.fn() as jest.MockedFunction<any>,
    listFragments: jest.fn() as jest.MockedFunction<any>,
    getClip: jest.fn() as jest.MockedFunction<any>,
} as any;

const mockVessel = {
    findOne: jest.fn() as jest.MockedFunction<any>,
    find: jest.fn() as jest.MockedFunction<any>,
} as any;

const MockVessel = jest.fn(() => mockVessel) as any;
MockVessel.findOne = mockVessel.findOne;
MockVessel.find = mockVessel.find;

const mockGetCloudfrontSignedUrl = jest.fn() as jest.MockedFunction<any>;

const createMockReturnValue = (value: any) => ({
    promise: (jest.fn() as any).mockResolvedValue(value)
});

const createMockRejectedValue = (value: any) => ({
    promise: (jest.fn() as any).mockRejectedValue(value)
});

const mockKinesisVideoClient = {
    send: jest.fn() as jest.MockedFunction<any>,
} as any;

const mockKinesisVideoArchivedMediaClient = {
    send: jest.fn() as jest.MockedFunction<any>,
} as any;

jest.mock('aws-sdk', () => {
    return {
        KinesisVideo: jest.fn(() => mockKinesisVideo) as any,
        KinesisVideoArchivedMedia: jest.fn(() => mockKinesisVideoArchivedMedia) as any,
        S3: jest.fn(() => ({
            upload: jest.fn() as any,
            deleteObject: jest.fn() as any,
            getObject: jest.fn() as any,
            listObjectsV2: jest.fn() as any,
            copyObject: jest.fn() as any,
            headObject: jest.fn() as any,
        })) as any,
        config: {
            update: jest.fn() as any,
        },
    };
});

jest.mock('@aws-sdk/client-kinesis-video', () => ({
    KinesisVideoClient: jest.fn(() => mockKinesisVideoClient) as any,
    GetDataEndpointCommand: jest.fn() as any,
}));

jest.mock('@aws-sdk/client-kinesis-video-archived-media', () => ({
    KinesisVideoArchivedMediaClient: jest.fn(() => mockKinesisVideoArchivedMediaClient) as any,
    GetImagesCommand: jest.fn() as any,
    ListFragmentsCommand: jest.fn() as any,
}));

jest.mock('../../models/Vessel', () => ({
    default: MockVessel,
    __esModule: true,
}));

jest.mock('../../modules/awsS3', () => ({
    getCloudfrontSignedUrl: mockGetCloudfrontSignedUrl,
    s3Config: {
        buckets: {
            assets: { name: 'test-bucket' }
        }
    }
}));

jest.mock('../../utils/functions', () => ({
    consoleLogObjectSize: jest.fn() as any,
}));

describe('AWS Kinesis Module', () => {
    beforeEach(() => {
        process.env = {
            ...originalEnv,
            NODE_ENV: 'test',
            AWS_ACCESS_KEY_ID: 'test-access-key',
            AWS_SECRET_ACCESS_KEY: 'test-secret-key',
            CLOUDFRONT_KEY_PAIR_ID: 'test-key-pair-id',
            CLOUDFRONT_PRIVATE_KEY: 'test-private-key'
        };

        jest.spyOn(global, 'setInterval').mockImplementation(() => 123 as any);

        jest.clearAllMocks();

        mockGetCloudfrontSignedUrl.mockReturnValue('https://test-cloudfront-url.com');
        try {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;
            if (awsKinesisModule.clearAllCachedDataEndpoints) {
                awsKinesisModule.clearAllCachedDataEndpoints();
            }
        } catch (_error) {
        }
    });

    afterEach(() => {
        process.env = originalEnv;
    });

    it('should throw error when AWS credentials are not set', () => {
        jest.resetModules();

        const originalAccessKey = process.env.AWS_ACCESS_KEY_ID;
        const originalSecretKey = process.env.AWS_SECRET_ACCESS_KEY;

        delete process.env.AWS_ACCESS_KEY_ID;
        delete process.env.AWS_SECRET_ACCESS_KEY;

        expect(() => {
            require('../../modules/awsKinesis');
        }).toThrow('AWS credentials are not set in env variables');

        process.env.AWS_ACCESS_KEY_ID = originalAccessKey;
        process.env.AWS_SECRET_ACCESS_KEY = originalSecretKey;
    });

    it('should export all required functions', () => {
        const awsKinesisModule = require('../../modules/awsKinesis').default as any;

        expect(typeof awsKinesisModule.listStreams).toBe('function');
        expect(typeof awsKinesisModule.getDashStreamingSessionURL).toBe('function');
        expect(typeof awsKinesisModule.getDashStreamingSessionURL_V2).toBe('function');
        expect(typeof awsKinesisModule.getClip).toBe('function');
        expect(typeof awsKinesisModule.getScreenshot).toBe('function');
        expect(typeof awsKinesisModule.getHlsStreamingSessionURL).toBe('function');
        expect(typeof awsKinesisModule.getStreamTags).toBe('function');
        expect(typeof awsKinesisModule.getLatestFragment).toBe('function');
    });

    describe('getStreamTags', () => {
        it('should return stream tags with vessel data', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockVessel.findOne.mockResolvedValue({
                name: 'Test Vessel',
                thumbnail_compressed_s3_key: 'test-thumbnail.jpg'
            });

            const result = await awsKinesisModule.getStreamTags({ streamName: 'test-stream' });

            expect(mockVessel.findOne).toHaveBeenCalledWith({ unit_id: 'test-stream' });
            expect(mockGetCloudfrontSignedUrl).toHaveBeenCalledWith({
                fileName: 'test-thumbnail.jpg',
                bucketName: 'test-bucket'
            });
            expect(result).toEqual({
                Name: 'Test Vessel',
                Thumbnail: 'https://test-cloudfront-url.com'
            });
        });

        it('should return default values when vessel not found', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockVessel.findOne.mockResolvedValue(null);

            const result = await awsKinesisModule.getStreamTags({ streamName: 'test-stream' });

            expect(result).toEqual({
                Name: 'Unregistered',
                Thumbnail: null
            });
        });

        it('should handle vessel with missing name', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockVessel.findOne.mockResolvedValue({
                name: null,
                thumbnail_compressed_s3_key: 'test-thumbnail.jpg'
            });

            const result = await awsKinesisModule.getStreamTags({ streamName: 'test-stream' });

            expect(result).toEqual({
                Name: 'Unregistered',
                Thumbnail: 'https://test-cloudfront-url.com'
            });
        });

        it('should handle vessel with missing thumbnail', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockVessel.findOne.mockResolvedValue({
                name: 'Test Vessel',
                thumbnail_compressed_s3_key: null
            });

            const result = await awsKinesisModule.getStreamTags({ streamName: 'test-stream' });

            expect(result).toEqual({
                Name: 'Test Vessel',
                Thumbnail: null
            });
        });

        it('should handle ClientLimitExceededException in getStreamTags', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockVessel.findOne.mockResolvedValue({
                name: 'Test Vessel',
                thumbnail_compressed_s3_key: 'test-thumbnail.jpg'
            });

            const result = await awsKinesisModule.getStreamTags({ streamName: 'test-stream' });

            expect(result).toEqual({
                Name: 'Test Vessel',
                Thumbnail: 'https://test-cloudfront-url.com'
            });
        });
    });

    describe('getDashStreamingSessionURL', () => {
        it('should return DASH streaming URL', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockReturnValue(
                createMockReturnValue({
                    DASHStreamingSessionURL: 'https://test-dash-url.com'
                })
            );

            const result = await awsKinesisModule.getDashStreamingSessionURL({
                streamName: 'test-stream',
                region: 'us-east-1',
                startTimestamp: new Date('2023-01-01'),
                endTimestamp: new Date('2023-01-02')
            });

            expect(mockKinesisVideo.getDataEndpoint).toHaveBeenCalled();
            expect(mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL).toHaveBeenCalled();
            expect(result).toBe('https://test-dash-url.com');
        });

        it('should handle data endpoint errors', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockRejectedValue(new Error('Data endpoint error'))
            );

            await expect(awsKinesisModule.getDashStreamingSessionURL({
                streamName: 'test-stream',
                region: 'us-east-1',
                startTimestamp: new Date('2023-01-01'),
                endTimestamp: new Date('2023-01-02')
            })).rejects.toThrow('Data endpoint error');
        });
    });

    describe('getDashStreamingSessionURL_V2', () => {
        it('should return DASH streaming URL V2', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockReturnValue({
                promise: (jest.fn() as any).mockResolvedValue({
                    DASHStreamingSessionURL: 'https://test-dash-v2-url.com'
                })
            });

            const result = await awsKinesisModule.getDashStreamingSessionURL_V2({
                streamName: 'test-stream',
                region: 'us-east-1',
                startTimestamp: new Date('2023-01-01'),
                endTimestamp: new Date('2023-01-02')
            });

            expect(result).toEqual({ url: 'https://test-dash-v2-url.com' });
        });

        it('should handle errors in getDashStreamingSessionURL_V2', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockReturnValue({
                promise: (jest.fn() as any).mockRejectedValue(new Error('DASH V2 error'))
            });

            await expect(awsKinesisModule.getDashStreamingSessionURL_V2({
                streamName: 'test-stream',
                region: 'us-east-1',
                startTimestamp: new Date('2023-01-01'),
                endTimestamp: new Date('2023-01-02')
            })).rejects.toThrow('DASH V2 error');
        });
    });

    describe('getHlsStreamingSessionURL', () => {
        it('should return HLS streaming URL', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getHLSStreamingSessionURL.mockReturnValue({
                promise: (jest.fn() as any).mockResolvedValue({
                    HLSStreamingSessionURL: 'https://test-hls-url.com'
                })
            });

            const result = await awsKinesisModule.getHlsStreamingSessionURL({
                streamName: 'test-stream',
                region: 'us-east-1',
                startTimestamp: new Date('2023-01-01'),
                endTimestamp: new Date('2023-01-02')
            });

            expect(result).toBe('https://test-hls-url.com');
        });

        it('should handle errors in getHlsStreamingSessionURL', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getHLSStreamingSessionURL.mockReturnValue({
                promise: (jest.fn() as any).mockRejectedValue(new Error('HLS error'))
            });

            await expect(awsKinesisModule.getHlsStreamingSessionURL({
                streamName: 'test-stream',
                region: 'us-east-1',
                startTimestamp: new Date('2023-01-01'),
                endTimestamp: new Date('2023-01-02')
            })).rejects.toThrow('HLS error');
        });
    });

    describe('getClip', () => {
        it('should return clip data', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getClip.mockReturnValue({
                promise: (jest.fn() as any).mockResolvedValue({
                    ContentType: 'video/mp4',
                    Payload: Buffer.from('test-clip-data')
                })
            });

            const result = await awsKinesisModule.getClip('test-stream', 'us-east-1', 1672531200000, 1672617600000);

            expect(result).toBeDefined();
            expect(result.ContentType).toBe('video/mp4');
        });

        it('should handle errors in getClip', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getClip.mockReturnValue({
                promise: (jest.fn() as any).mockRejectedValue(new Error('Clip error'))
            });

            await expect(awsKinesisModule.getClip('test-stream', 'us-east-1', 1672531200000, 1672617600000))
                .rejects.toThrow('Clip error');
        });

        it('should handle ClientLimitExceededException in getDashStreamingSessionURL_V2', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockReturnValue({
                promise: (jest.fn() as any)
                    .mockRejectedValueOnce({
                        code: 'ClientLimitExceededException',
                        retryDelay: '2'
                    })
                    .mockResolvedValueOnce({
                        DASHStreamingSessionURL: 'https://test-dash-url.com'
                    })
            });

            const result = await awsKinesisModule.getDashStreamingSessionURL_V2({
                streamName: 'test-stream',
                region: 'us-east-1',
                streamMode: 'ON_DEMAND',
                startTimestamp: new Date('2023-01-01'),
                endTimestamp: new Date('2023-01-02')
            });

            expect(result).toEqual({ url: 'https://test-dash-url.com' });
        });

        it('should handle ResourceNotFoundException and fetch latest fragment', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockReturnValue({
                promise: (jest.fn() as any).mockRejectedValue({
                    code: 'ResourceNotFoundException',
                    message: 'Stream not found'
                })
            });

            mockKinesisVideoClient.send
                .mockResolvedValueOnce({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
                .mockResolvedValueOnce({
                    Fragments: [{
                        FragmentNumber: '1234567890',
                        FragmentSizeInBytes: 1024,
                        ProducerTimestamp: new Date('2023-01-01'),
                        ServerTimestamp: new Date('2023-01-01')
                    }]
                });

            mockKinesisVideoArchivedMediaClient.send.mockResolvedValue({
                Fragments: [{
                    FragmentNumber: '1234567890',
                    FragmentSizeInBytes: 1024,
                    ProducerTimestamp: new Date('2023-01-01'),
                    ServerTimestamp: new Date('2023-01-01')
                }]
            });

            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockReturnValue({
                promise: (jest.fn() as any).mockResolvedValue({
                    DASHStreamingSessionURL: 'https://test-dash-url-from-fragment.com'
                })
            });

            const result = await awsKinesisModule.getDashStreamingSessionURL_V2({
                streamName: 'test-stream',
                region: 'us-east-1',
                streamMode: 'ON_DEMAND',
                startTimestamp: new Date('2023-01-01'),
                endTimestamp: new Date('2023-01-02')
            });

            expect(result).toEqual({ url: 'https://test-dash-url-from-fragment.com' });
        });

        it('should handle ResourceNotFoundException when no latest fragment found', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockReturnValue({
                promise: (jest.fn() as any).mockRejectedValue({
                    code: 'ResourceNotFoundException',
                    message: 'Stream not found'
                })
            });

            mockKinesisVideoClient.send
                .mockResolvedValueOnce({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
                .mockResolvedValueOnce({
                    Fragments: []
                });

            mockKinesisVideoArchivedMediaClient.send.mockResolvedValue({
                Fragments: []
            });

            await expect(awsKinesisModule.getDashStreamingSessionURL_V2({
                streamName: 'test-stream',
                region: 'us-east-1',
                streamMode: 'ON_DEMAND',
                startTimestamp: new Date('2023-01-01'),
                endTimestamp: new Date('2023-01-02')
            })).rejects.toThrow('No streams found in the specified timestamp range.');
        });

        it('should handle ResourceNotFoundException in LIVE mode', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockReturnValue({
                promise: (jest.fn() as any).mockRejectedValue({
                    code: 'ResourceNotFoundException',
                    message: 'Stream not found'
                })
            });

            await expect(awsKinesisModule.getDashStreamingSessionURL_V2({
                streamName: 'test-stream',
                region: 'us-east-1',
                streamMode: 'LIVE',
                startTimestamp: new Date('2023-01-01'),
                endTimestamp: new Date('2023-01-02')
            })).rejects.toThrow('No streams found in the specified timestamp range.');
        });
    });

    describe('getScreenshot', () => {
        it('should return screenshot as buffer', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideoClient.send.mockResolvedValue({
                DataEndpoint: 'https://test-endpoint.amazonaws.com'
            });

            mockKinesisVideoArchivedMediaClient.send.mockResolvedValue({
                Images: [{
                    TimeStamp: new Date('2023-01-01'),
                    ImageContent: Buffer.from('test-image-data')
                }]
            });

            const result = await awsKinesisModule.getScreenshot('test-stream', 'us-east-1', 1672531200000, 'JPEG');

            expect(result).toBeInstanceOf(Buffer);
        });



        it('should handle missing data endpoint in getScreenshot', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideoClient.send.mockResolvedValue({
                DataEndpoint: null
            });

            await expect(awsKinesisModule.getScreenshot('test-stream', 'us-east-1', 1672531200000, 'JPEG'))
                .rejects.toThrow('Could not retrieve data endpoint for GetImages.');
        });


    });

    describe('getLatestFragment', () => {
        it('should return latest fragment', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideoClient.send.mockClear();
            mockKinesisVideoArchivedMediaClient.send.mockClear();

            mockKinesisVideoClient.send.mockResolvedValue({
                DataEndpoint: 'https://test-endpoint.amazonaws.com'
            });

            mockKinesisVideoArchivedMediaClient.send.mockResolvedValue({
                Fragments: [{
                    FragmentNumber: '1234567890',
                    FragmentSizeInBytes: 1024,
                    ProducerTimestamp: new Date('2023-01-01'),
                    ServerTimestamp: new Date('2023-01-01')
                }]
            });

            const result = await awsKinesisModule.getLatestFragment({
                streamName: 'test-stream',
                region: 'us-east-1',
                startTime: 1672531200000,
                endTime: 1672617600000
            });

            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.FragmentNumber).toBe('1234567890');
        });

        it('should return null when no fragments found', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideoClient.send.mockResolvedValue({
                DataEndpoint: 'https://test-endpoint.amazonaws.com'
            });

            mockKinesisVideoArchivedMediaClient.send.mockResolvedValue({
                Fragments: []
            });

            const result = await awsKinesisModule.getLatestFragment({
                streamName: 'test-stream',
                region: 'us-east-1',
                startTime: 1672531200000,
                endTime: 1672617600000
            });

            expect(result).toBeNull();
        });

        it('should handle errors in getLatestFragment', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue({
                promise: (jest.fn() as any).mockRejectedValue(new Error('Fragment error'))
            });

            await expect(awsKinesisModule.getLatestFragment({
                streamName: 'test-stream',
                region: 'us-east-1',
                startTime: 1672531200000,
                endTime: 1672617600000
            })).rejects.toThrow('Fragment error');
        });

        it('should handle ClientLimitExceededException in getLatestFragment getDataEndpoint', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue({
                promise: (jest.fn() as any)
                    .mockRejectedValueOnce({
                        code: 'ClientLimitExceededException',
                        retryDelay: '2'
                    })
                    .mockResolvedValueOnce({
                        DataEndpoint: 'https://test-endpoint.amazonaws.com'
                    })
            });

            mockKinesisVideoArchivedMediaClient.send.mockResolvedValue({
                Fragments: [{
                    FragmentNumber: '1234567890',
                    FragmentSizeInBytes: 1024,
                    ProducerTimestamp: new Date('2023-01-01'),
                    ServerTimestamp: new Date('2023-01-01')
                }]
            });

            const result = await awsKinesisModule.getLatestFragment({
                streamName: 'test-stream',
                region: 'us-east-1',
                startTime: 1672531200000,
                endTime: 1672617600000
            });

            expect(result).toBeDefined();
            expect(result.FragmentNumber).toBe('1234567890');
        });

        it('should handle ClientLimitExceededException in getLatestFragment getListFragments', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue({
                promise: (jest.fn() as any).mockResolvedValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            });

            mockKinesisVideoArchivedMediaClient.send
                .mockRejectedValueOnce({
                    code: 'ClientLimitExceededException',
                    retryDelay: '2'
                })
                .mockResolvedValueOnce({
                    Fragments: [{
                        FragmentNumber: '1234567890',
                        FragmentSizeInBytes: 1024,
                        ProducerTimestamp: new Date('2023-01-01'),
                        ServerTimestamp: new Date('2023-01-01')
                    }]
                });

            const result = await awsKinesisModule.getLatestFragment({
                streamName: 'test-stream',
                region: 'us-east-1',
                startTime: 1672531200000,
                endTime: 1672617600000
            });

            expect(result).toBeDefined();
            expect(result.FragmentNumber).toBe('1234567890');
        });

        it('should handle ClientLimitExceededException in getLatestFragment catch block', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue({
                promise: (jest.fn() as any)
                    .mockRejectedValueOnce({
                        name: 'ClientLimitExceededException',
                        retryDelay: '1'
                    })
                    .mockResolvedValueOnce({
                        DataEndpoint: 'https://test-endpoint.amazonaws.com'
                    })
            });

            mockKinesisVideoArchivedMediaClient.send.mockResolvedValue({
                Fragments: [{
                    FragmentNumber: '1234567890',
                    FragmentSizeInBytes: 1024,
                    ProducerTimestamp: new Date('2023-01-01'),
                    ServerTimestamp: new Date('2023-01-01')
                }]
            });

            const result = await awsKinesisModule.getLatestFragment({
                streamName: 'test-stream',
                region: 'us-east-1',
                startTime: 1672531200000,
                endTime: 1672617600000
            });

            expect(result).toBeDefined();
            expect(result.FragmentNumber).toBe('1234567890');
        }, 15000);

        it('should throw error when getListFragments fails with non-ClientLimitExceededException', async () => {
            jest.resetModules();
            jest.clearAllMocks();

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMediaClient.send.mockRejectedValue({
                code: 'SomeOtherError',
                message: 'Some other error'
            });

            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            await expect(awsKinesisModule.getLatestFragment({
                streamName: 'test-stream',
                region: 'us-east-1',
                startTime: 1672531200000,
                endTime: 1672617600000
            })).rejects.toMatchObject({
                code: 'SomeOtherError',
                message: 'Some other error'
            });
        });
    });

    describe('Error handling', () => {
        it('should handle data endpoint errors', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockRejectedValue(new Error('Data endpoint error'))
            );

            await expect(awsKinesisModule.getDashStreamingSessionURL({
                streamName: 'test-stream',
                region: 'us-east-1',
                startTimestamp: new Date('2023-01-01'),
                endTimestamp: new Date('2023-01-02')
            })).rejects.toThrow('Data endpoint error');
        });
    });

    describe('listStreams', () => {
        it('should return streams with tags and live status', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.listStreams.mockReturnValue({
                promise: (jest.fn() as any).mockResolvedValue({
                    StreamInfoList: [{
                        StreamName: 'test-stream-1',
                        StreamARN: 'arn:aws:kinesisvideo:us-east-1:123456789012:stream/test-stream-1',
                        StreamStatus: 'ACTIVE',
                        CreationTime: new Date('2023-01-01')
                    }]
                })
            });

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockReturnValue(
                createMockReturnValue({
                    DASHStreamingSessionURL: 'https://test-dash-url.com'
                })
            );

            mockVessel.findOne.mockResolvedValue({
                name: 'Test Vessel 1',
                thumbnail_compressed_s3_key: 'test-thumbnail-1.jpg'
            });

            const result = await awsKinesisModule.listStreams({ region: 'us-east-1' });

            expect(result).toHaveLength(1);
            expect(result[0]).toMatchObject({
                StreamName: 'test-stream-1',
                StreamARN: 'arn:aws:kinesisvideo:us-east-1:123456789012:stream/test-stream-1',
                StreamStatus: 'ACTIVE',
                Region: 'us-east-1',
                IsLive: true,
                Tags: {
                    Name: 'Test Vessel 1',
                    Thumbnail: 'https://test-cloudfront-url.com'
                }
            });
        });

        it('should handle ResourceNotFoundException gracefully', async () => {
            jest.resetModules();
            jest.clearAllMocks();

            mockKinesisVideo.listStreams.mockReturnValue({
                promise: (jest.fn() as any).mockRejectedValue({
                    code: 'ResourceNotFoundException',
                    message: 'Stream not found'
                })
            });

            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            await expect(awsKinesisModule.listStreams({ region: 'us-east-1' }))
                .rejects.toMatchObject({
                    code: 'ResourceNotFoundException'
                });
        });

        it('should handle ClientLimitExceededException with retry', async () => {
            jest.resetModules();
            jest.clearAllMocks();

            mockKinesisVideo.listStreams.mockReturnValue({
                promise: (jest.fn() as any)
                    .mockRejectedValueOnce({
                        code: 'ClientLimitExceededException',
                        retryDelay: '1'
                    })
                    .mockResolvedValueOnce({
                        StreamInfoList: []
                    })
            });

            const awsKinesisModule = require('../../modules/awsKinesis').default as any;
            const result = await awsKinesisModule.listStreams({ region: 'us-east-1' });
            expect(result).toEqual([]);
        }, 15000);
    });

    describe('getStreamsList', () => {
        it('should return cached streams when available', async () => {
            jest.resetModules();
            jest.clearAllMocks();

            mockKinesisVideo.listStreams.mockReturnValue({
                promise: (jest.fn() as any).mockResolvedValue({
                    StreamInfoList: [{
                        StreamName: 'test-stream-1',
                        StreamARN: 'arn:aws:kinesisvideo:us-east-1:123456789012:stream/test-stream-1',
                        StreamStatus: 'ACTIVE'
                    }]
                })
            });

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockReturnValue(
                createMockReturnValue({
                    DASHStreamingSessionURL: 'https://test-dash-url.com'
                })
            );

            mockVessel.findOne.mockResolvedValue({
                name: 'Test Vessel',
                thumbnail_compressed_s3_key: 'test-thumbnail.jpg'
            });

            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            await awsKinesisModule.listStreams({ region: 'us-east-1' });

            const result = await awsKinesisModule.listStreams({ region: 'us-east-1' });

            expect(mockKinesisVideo.listStreams).toHaveBeenCalledTimes(1);
            expect(result).toHaveLength(1);
        });

        it('should handle pagination with NextToken', async () => {
            jest.resetModules();
            jest.clearAllMocks();

            mockKinesisVideo.listStreams.mockReturnValue({
                promise: (jest.fn() as any)
                    .mockResolvedValueOnce({
                        StreamInfoList: [{
                            StreamName: 'test-stream-1',
                            StreamARN: 'arn:aws:kinesisvideo:us-east-1:123456789012:stream/test-stream-1',
                            StreamStatus: 'ACTIVE'
                        }],
                        NextToken: 'next-token-1'
                    })
                    .mockResolvedValueOnce({
                        StreamInfoList: [{
                            StreamName: 'test-stream-2',
                            StreamARN: 'arn:aws:kinesisvideo:us-east-1:123456789012:stream/test-stream-2',
                            StreamStatus: 'ACTIVE'
                        }]
                    })
            });

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockReturnValue(
                createMockReturnValue({
                    DASHStreamingSessionURL: 'https://test-dash-url.com'
                })
            );

            mockVessel.findOne.mockResolvedValue({
                name: 'Test Vessel',
                thumbnail_compressed_s3_key: 'test-thumbnail.jpg'
            });

            const awsKinesisModule = require('../../modules/awsKinesis').default as any;
            const result = await awsKinesisModule.listStreams({ region: 'us-east-1' });

            expect(result).toHaveLength(2);
            expect(mockKinesisVideo.listStreams).toHaveBeenCalledTimes(2);
        });
    });

    describe('getStreamLiveStatus', () => {
        it('should return true when stream is live', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.listStreams.mockReturnValue({
                promise: (jest.fn() as any).mockResolvedValue({
                    StreamInfoList: [{
                        StreamName: 'test-stream-1',
                        StreamARN: 'arn:aws:kinesisvideo:us-east-1:123456789012:stream/test-stream-1',
                        StreamStatus: 'ACTIVE'
                    }]
                })
            });

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockReturnValue(
                createMockReturnValue({
                    DASHStreamingSessionURL: 'https://test-dash-url.com'
                })
            );

            mockVessel.findOne.mockResolvedValue({
                name: 'Test Vessel',
                thumbnail_compressed_s3_key: 'test-thumbnail.jpg'
            });

            const result = await awsKinesisModule.listStreams({ region: 'us-east-1' });

            expect(result[0].IsLive).toBe(true);
        });

        it('should return false when stream is not live', async () => {
            jest.resetModules();
            jest.clearAllMocks();

            mockKinesisVideo.listStreams.mockReturnValue({
                promise: (jest.fn() as any).mockResolvedValue({
                    StreamInfoList: [{
                        StreamName: 'test-stream-1',
                        StreamARN: 'arn:aws:kinesisvideo:us-east-1:123456789012:stream/test-stream-1',
                        StreamStatus: 'ACTIVE'
                    }]
                })
            });

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockReturnValue({
                promise: (jest.fn() as any).mockResolvedValue({
                    DASHStreamingSessionURL: null
                })
            });

            mockVessel.findOne.mockResolvedValue({
                name: 'Test Vessel',
                thumbnail_compressed_s3_key: 'test-thumbnail.jpg'
            });

            const awsKinesisModule = require('../../modules/awsKinesis').default as any;
            const result = await awsKinesisModule.listStreams({ region: 'us-east-1' });

            expect(result[0].IsLive).toBe(false);
        });

        it('should handle ResourceNotFoundException and return false', async () => {
            jest.resetModules();
            jest.clearAllMocks();

            mockKinesisVideo.listStreams.mockReturnValue({
                promise: (jest.fn() as any).mockResolvedValue({
                    StreamInfoList: [{
                        StreamName: 'test-stream-1',
                        StreamARN: 'arn:aws:kinesisvideo:us-east-1:123456789012:stream/test-stream-1',
                        StreamStatus: 'ACTIVE'
                    }]
                })
            });

            mockKinesisVideo.getDataEndpoint.mockReturnValue({
                promise: (jest.fn() as any).mockRejectedValue({
                    code: 'ResourceNotFoundException',
                    message: 'Stream not found'
                })
            });

            mockVessel.findOne.mockResolvedValue({
                name: 'Test Vessel',
                thumbnail_compressed_s3_key: 'test-thumbnail.jpg'
            });

            const awsKinesisModule = require('../../modules/awsKinesis').default as any;
            const result = await awsKinesisModule.listStreams({ region: 'us-east-1' });

            expect(result[0].IsLive).toBe(false);
        });

        it('should use cached live status when available', async () => {
            jest.resetModules();
            jest.clearAllMocks();

            mockKinesisVideo.listStreams.mockReturnValue({
                promise: (jest.fn() as any).mockResolvedValue({
                    StreamInfoList: [{
                        StreamName: 'test-stream-1',
                        StreamARN: 'arn:aws:kinesisvideo:us-east-1:123456789012:stream/test-stream-1',
                        StreamStatus: 'ACTIVE'
                    }]
                })
            });

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockReturnValue(
                createMockReturnValue({
                    DASHStreamingSessionURL: 'https://test-dash-url.com'
                })
            );

            mockVessel.findOne.mockResolvedValue({
                name: 'Test Vessel',
                thumbnail_compressed_s3_key: 'test-thumbnail.jpg'
            });

            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            await awsKinesisModule.listStreams({ region: 'us-east-1' });

            await awsKinesisModule.listStreams({ region: 'us-east-1' });

            expect(mockKinesisVideo.getDataEndpoint).toHaveBeenCalledTimes(1);
        });
    });

    describe('getDataEndPoint', () => {
        it('should return cached data endpoint when available', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            if (awsKinesisModule.clearAllCachedDataEndpoints) {
                awsKinesisModule.clearAllCachedDataEndpoints();
            }
            mockKinesisVideo.getDataEndpoint.mockClear();
            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockClear();

            const uniqueStreamName = 'test-stream-cache-' + Date.now() + '-' + Math.random();

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockReturnValue(
                createMockReturnValue({
                    DASHStreamingSessionURL: 'https://test-dash-url.com'
                })
            );

            await awsKinesisModule.getDashStreamingSessionURL_V2({
                streamName: uniqueStreamName,
                region: 'us-east-1',
                streamMode: 'LIVE',
                startTimestamp: 1672531200000,
                totalDuration: 60
            });

            await awsKinesisModule.getDashStreamingSessionURL_V2({
                streamName: uniqueStreamName,
                region: 'us-east-1',
                streamMode: 'LIVE',
                startTimestamp: 1672531200000,
                totalDuration: 60
            });

            expect(mockKinesisVideo.getDataEndpoint).toHaveBeenCalledTimes(1);
        });

        it('should handle ClientLimitExceededException with retry', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue({
                promise: (jest.fn() as any)
                    .mockRejectedValueOnce({
                        code: 'ClientLimitExceededException',
                        retryDelay: '1'
                    })
                    .mockResolvedValueOnce({
                        DataEndpoint: 'https://test-endpoint.amazonaws.com'
                    })
            });

            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockReturnValue(
                createMockReturnValue({
                    DASHStreamingSessionURL: 'https://test-dash-url.com'
                })
            );

            const result = await awsKinesisModule.getDashStreamingSessionURL({
                streamName: 'test-stream',
                region: 'us-east-1',
                streamMode: 'LIVE'
            });

            expect(result).toBe('https://test-dash-url.com');
        }, 15000);

    });

    describe('handleClientLimitExceededException', () => {
        it('should retry after delay when ClientLimitExceededException occurs', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue({
                promise: (jest.fn() as any)
                    .mockRejectedValueOnce({
                        code: 'ClientLimitExceededException',
                        retryDelay: '1'
                    })
                    .mockResolvedValueOnce({
                        DataEndpoint: 'https://test-endpoint.amazonaws.com'
                    })
            });

            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockReturnValue(
                createMockReturnValue({
                    DASHStreamingSessionURL: 'https://test-dash-url.com'
                })
            );

            const startTime = Date.now();
            const result = await awsKinesisModule.getDashStreamingSessionURL({
                streamName: 'test-stream',
                region: 'us-east-1',
                streamMode: 'LIVE'
            });
            const endTime = Date.now();

            expect(result).toBe('https://test-dash-url.com');
            expect(endTime - startTime).toBeGreaterThanOrEqual(1000);
        }, 15000);

        it('should use default retry delay when not provided', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue({
                promise: (jest.fn() as any)
                    .mockRejectedValueOnce({
                        code: 'ClientLimitExceededException'
                    })
                    .mockResolvedValueOnce({
                        DataEndpoint: 'https://test-endpoint.amazonaws.com'
                    })
            });

            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockReturnValue(
                createMockReturnValue({
                    DASHStreamingSessionURL: 'https://test-dash-url.com'
                })
            );

            const startTime = Date.now();
            const result = await awsKinesisModule.getDashStreamingSessionURL({
                streamName: 'test-stream',
                region: 'us-east-1',
                streamMode: 'LIVE'
            });
            const endTime = Date.now();

            expect(result).toBe('https://test-dash-url.com');
            expect(endTime - startTime).toBeGreaterThanOrEqual(2000);
        }, 15000);
    });

    describe('getDashStreamingSessionURL_V2 edge cases', () => {
        it('should handle UnknownEndpoint error and retry', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue({
                promise: (jest.fn() as any)
                    .mockResolvedValueOnce({
                        DataEndpoint: 'https://invalid-endpoint.amazonaws.com'
                    })
                    .mockResolvedValueOnce({
                        DataEndpoint: 'https://valid-endpoint.amazonaws.com'
                    })
            });

            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockReturnValue({
                promise: (jest.fn() as any)
                    .mockRejectedValueOnce({
                        code: 'UnknownEndpoint',
                        message: 'Unknown endpoint'
                    })
                    .mockResolvedValueOnce({
                        DASHStreamingSessionURL: 'https://test-dash-v2-url.com'
                    })
            });

            const result = await awsKinesisModule.getDashStreamingSessionURL_V2({
                streamName: 'test-stream',
                region: 'us-east-1',
                startTimestamp: 1672531200000,
                totalDuration: 60
            });

            expect(result).toEqual({ url: 'https://test-dash-v2-url.com' });
        });


        it('should throw error when no fragments found in ON_DEMAND mode', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockReturnValue({
                promise: (jest.fn() as any).mockRejectedValue({
                    code: 'ResourceNotFoundException',
                    message: 'Resource not found'
                })
            });

            mockKinesisVideoClient.send.mockResolvedValue({
                DataEndpoint: 'https://test-endpoint.amazonaws.com'
            });

            mockKinesisVideoArchivedMediaClient.send.mockResolvedValue({
                Fragments: []
            });

            await expect(awsKinesisModule.getDashStreamingSessionURL_V2({
                streamName: 'test-stream',
                region: 'us-east-1',
                streamMode: 'ON_DEMAND',
                startTimestamp: 1672531200000,
                totalDuration: 60
            })).rejects.toThrow('No streams found in the specified timestamp range.');
        });
    });

    describe('getClip error handling', () => {
        it('should handle ResourceNotFoundException', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getClip.mockReturnValue({
                promise: (jest.fn() as any).mockRejectedValue({
                    code: 'ResourceNotFoundException',
                    message: 'Stream not found'
                })
            });

            await expect(awsKinesisModule.getClip('test-stream', 'us-east-1', 1672531200000, 1672617600000))
                .rejects.toMatchObject({
                    code: 'ResourceNotFoundException'
                });
        });

        it('should handle InvalidArgumentException', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getClip.mockReturnValue({
                promise: (jest.fn() as any).mockRejectedValue({
                    code: 'InvalidArgumentException',
                    message: 'Invalid argument'
                })
            });

            await expect(awsKinesisModule.getClip('test-stream', 'us-east-1', 1672531200000, 1672617600000))
                .rejects.toMatchObject({
                    code: 'InvalidArgumentException'
                });
        });

        it('should handle NotAuthorizedException', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getClip.mockReturnValue({
                promise: (jest.fn() as any).mockRejectedValue({
                    code: 'NotAuthorizedException',
                    message: 'Not authorized'
                })
            });

            await expect(awsKinesisModule.getClip('test-stream', 'us-east-1', 1672531200000, 1672617600000))
                .rejects.toMatchObject({
                    code: 'NotAuthorizedException'
                });
        });

        it('should handle ClientLimitExceededException', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getClip.mockReturnValue({
                promise: (jest.fn() as any).mockRejectedValue({
                    code: 'ClientLimitExceededException',
                    message: 'Client limit exceeded'
                })
            });

            await expect(awsKinesisModule.getClip('test-stream', 'us-east-1', 1672531200000, 1672617600000))
                .rejects.toMatchObject({
                    code: 'ClientLimitExceededException'
                });
        });
    });

    describe('getScreenshot edge cases', () => {
        it('should retry on ResourceNotFoundException up to 3 times', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideoClient.send.mockResolvedValue({
                DataEndpoint: 'https://test-endpoint.amazonaws.com'
            });

            mockKinesisVideoArchivedMediaClient.send
                .mockRejectedValueOnce({
                    name: 'ResourceNotFoundException',
                    message: 'Resource not found'
                })
                .mockRejectedValueOnce({
                    name: 'ResourceNotFoundException',
                    message: 'Resource not found'
                })
                .mockResolvedValueOnce({
                    Images: [{
                        TimeStamp: new Date('2023-01-01'),
                        ImageContent: Buffer.from('test-image-data')
                    }]
                });

            const result = await awsKinesisModule.getScreenshot('test-stream', 'us-east-1', 1672531200000, 'JPEG');

            expect(result).toBeInstanceOf(Buffer);
            expect(mockKinesisVideoArchivedMediaClient.send).toHaveBeenCalledTimes(3);
        });



    });

    describe('getLatestFragment error handling', () => {
        it('should handle ClientLimitExceededException with retry', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideoClient.send
                .mockRejectedValueOnce({
                    code: 'ClientLimitExceededException',
                    retryDelay: '2'
                })
                .mockResolvedValueOnce({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                });

            mockKinesisVideoArchivedMediaClient.send.mockResolvedValue({
                Fragments: [{
                    FragmentNumber: '1234567890',
                    FragmentSizeInBytes: 1024,
                    ProducerTimestamp: new Date('2023-01-01'),
                    ServerTimestamp: new Date('2023-01-01')
                }]
            });

            const result = await awsKinesisModule.getLatestFragment({
                streamName: 'test-stream',
                region: 'us-east-1',
                startTime: 1672531200000,
                endTime: 1672617600000
            });

            expect(result).toBeDefined();
            expect(result.FragmentNumber).toBe('1234567890');
        });

        it('should handle ClientLimitExceededException with name property', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            mockKinesisVideoClient.send
                .mockRejectedValueOnce({
                    name: 'ClientLimitExceededException',
                    retryDelay: '2'
                })
                .mockResolvedValueOnce({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                });

            mockKinesisVideoArchivedMediaClient.send.mockResolvedValue({
                Fragments: [{
                    FragmentNumber: '1234567890',
                    FragmentSizeInBytes: 1024,
                    ProducerTimestamp: new Date('2023-01-01'),
                    ServerTimestamp: new Date('2023-01-01')
                }]
            });

            const result = await awsKinesisModule.getLatestFragment({
                streamName: 'test-stream',
                region: 'us-east-1',
                startTime: 1672531200000,
                endTime: 1672617600000
            });

            expect(result).toBeDefined();
            expect(result.FragmentNumber).toBe('1234567890');
        });
    });

    describe('Caching behavior', () => {
        it('should use cached data endpoints when available', async () => {
            const awsKinesisModule = require('../../modules/awsKinesis').default as any;

            if (awsKinesisModule.clearAllCachedDataEndpoints) {
                awsKinesisModule.clearAllCachedDataEndpoints();
            }
            mockKinesisVideo.getDataEndpoint.mockClear();
            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockClear();

            const uniqueStreamName = 'test-stream-cache-2-' + Date.now() + '-' + Math.random();

            mockKinesisVideo.getDataEndpoint.mockReturnValue(
                createMockReturnValue({
                    DataEndpoint: 'https://test-endpoint.amazonaws.com'
                })
            );

            mockKinesisVideoArchivedMedia.getDASHStreamingSessionURL.mockReturnValue(
                createMockReturnValue({
                    DASHStreamingSessionURL: 'https://test-dash-url.com'
                })
            );

            await awsKinesisModule.getDashStreamingSessionURL_V2({
                streamName: uniqueStreamName,
                region: 'us-east-1',
                streamMode: 'LIVE',
                startTimestamp: 1672531200000,
                totalDuration: 60
            });

            await awsKinesisModule.getDashStreamingSessionURL_V2({
                streamName: uniqueStreamName,
                region: 'us-east-1',
                streamMode: 'LIVE',
                startTimestamp: 1672531200000,
                totalDuration: 60
            });

            expect(mockKinesisVideo.getDataEndpoint).toHaveBeenCalledTimes(1);
        });
    });

    describe('Module initialization', () => {
        it('should initialize setInterval for cache monitoring', () => {
            const setIntervalSpy = jest.spyOn(global, 'setInterval').mockImplementation(() => 123 as any);

            jest.resetModules();

            require('../../modules/awsKinesis').default as any;

            expect(setIntervalSpy).not.toHaveBeenCalled();

            setIntervalSpy.mockRestore();
        });

    });
});