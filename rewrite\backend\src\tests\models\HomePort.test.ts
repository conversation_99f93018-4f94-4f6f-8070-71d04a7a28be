import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('HomePort Model', () => {
    let mockMongoose: any;
    let mockDb: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create HomePort model with proper schema and hooks', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/HomePort')];

        const HomePortModule = await import('../../models/HomePort');
        const HomePort = HomePortModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('HomePort', expect.any(Object), 'home_ports');
        expect(HomePort).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.lat).toBeDefined();
        expect(schemaArg.paths.lat.type).toBe(Number);
        expect(schemaArg.paths.lat.required).toBe(true);

        expect(schemaArg.paths.lng).toBeDefined();
        expect(schemaArg.paths.lng.type).toBe(Number);
        expect(schemaArg.paths.lng.required).toBe(true);
    });
});
