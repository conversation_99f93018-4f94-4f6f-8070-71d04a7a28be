import { describe, it, beforeEach, expect, jest } from '@jest/globals';
import thingsboardService from '../../services/Thingsboard.service';
import ThingsboardDevices from '../../models/ThingsboardDevices';
import microserviceSocket from '../../modules/microservice_socket';

jest.mock('../../models/ThingsboardDevices', () => require('../mocks/models/thingsboardDevices.mock'));
jest.mock('../../modules/microservice_socket', () => require('../mocks/modules/microservice_socket.mock'));

describe('ThingsboardService', () => {
    beforeEach(() => {
        jest.resetAllMocks();
    });

    it('getAllDevices returns projected devices', async () => {
        (ThingsboardDevices.find as jest.Mock).mockResolvedValue([{ deviceId: 'd1' }] as never);
        const res = await thingsboardService.getAllDevices();
        expect(res).toEqual([{ deviceId: 'd1' }]);
        expect(ThingsboardDevices.find).toHaveBeenCalledWith({}, { deviceId: 1, deviceName: 1, accessToken: 1, dashboardId: 1 });
    });

    it('getDeviceByUnitId returns single projected device', async () => {
        (ThingsboardDevices.findOne as jest.Mock).mockResolvedValue({ deviceName: 'U1' } as never);
        const res = await thingsboardService.getDeviceByUnitId('U1');
        expect(res).toEqual({ deviceName: 'U1' });
        expect(ThingsboardDevices.findOne).toHaveBeenCalledWith({ deviceName: 'U1' }, { deviceId: 1, deviceName: 1, accessToken: 1, dashboardId: 1 });
    });

    it('resetDashboards emits event', async () => {
        const spy = jest.spyOn(microserviceSocket, 'emit');
        thingsboardService.resetDashboards();
        expect(spy).toHaveBeenCalledWith('thingsboard/reset-dashboard');
    });
});


