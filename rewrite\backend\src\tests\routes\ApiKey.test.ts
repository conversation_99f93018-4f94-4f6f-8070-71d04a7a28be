import User from '../../models/User';
import { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } from '../data/Auth';
import ApiKey from '../../models/ApiKey';
import apiKeyService from '../../services/ApiKey.service';
import request from 'supertest';
import app from '../../server';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import mongoose from 'mongoose';
import { apiKeysList } from '../data/ApiKeys';
import { setupAuthMocks, setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../models/ApiEndpoint', () => require('../mocks/models/apiEndpoint.mock'));

describe('API Keys API', () => {

    describe('GET /api/apiKeys', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/apiKeys');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if the user does not have the required permissions', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/apiKeys')
                        .set('Authorization', nonAuthToken);

                    expect(res.status).toBe(403);
                });

                it('should return 200 and fetch the list of API keys if the user is authorized or 403 for api-key', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    jest.spyOn(apiKeyService, 'fetchAll').mockResolvedValue(apiKeysList as any);

                    const res = await request(app)
                        .get('/api/apiKeys')
                        .set('Authorization', authToken);

                    if (authMethod === 'user') {
                        expect(res.status).toBe(200);
                        ['_id', 'description', 'allowed_endpoints', 'is_deleted', 'is_revoked', 'api_key', 'requests', 'jwt_token', 'creation_timestamp'].forEach(prop => {
                            expect(res.body[0]).toHaveProperty(prop);
                        });
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });

                it('should return 500 if an error occurs while fetching the API keys', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    jest.spyOn(apiKeyService, 'fetchAll').mockRejectedValueOnce(new Error('Database error'));

                    const res = await request(app)
                        .get('/api/apiKeys')
                        .set('Authorization', authToken);

                    if (authMethod === 'user') {
                        expect(res.status).toBe(500);
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/apiKeys', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/apiKeys');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if the user does not have the required permissions', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/apiKeys')
                        .set('Authorization', nonAuthToken)
                        .send({ description: 'Test API Key' });

                    expect(res.status).toBe(403);
                });

                it('should return 400 if description is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/apiKeys')
                        .set('Authorization', authToken)
                        .send({});

                    if (authMethod === 'user') {
                        expect(res.status).toBe(400);
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });

                it('should return 400 if description is an empty string', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/apiKeys')
                        .set('Authorization', authToken)
                        .send({ description: '' });

                    if (authMethod === 'user') {
                        expect(res.status).toBe(400);
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });

                it('should return 400 if email is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/apiKeys')
                        .set('Authorization', authToken)
                        .send({ description: 'Test API Key', email: 'not-an-email' });

                    if (authMethod === 'user') {
                        expect(res.status).toBe(400);
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });

                it('should return 200 and create a new API key', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    jest.spyOn(apiKeyService, 'create').mockResolvedValueOnce({ _id: new mongoose.Types.ObjectId(), description: 'Test API Key' } as any);

                    const res = await request(app)
                        .post('/api/apiKeys')
                        .set('Authorization', authToken)
                        .send({ description: 'Test API Key' });

                    if (authMethod === 'user') {
                        expect(res.status).toBe(200);
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });

                it('should return 500 if an error occurs during the creation of the API key', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    jest.spyOn(apiKeyService, 'create').mockRejectedValueOnce(new Error('Database error'));

                    const res = await request(app)
                        .post('/api/apiKeys')
                        .set('Authorization', authToken)
                        .send({ description: 'Test API Key' });

                    if (authMethod === 'user') {
                        expect(res.status).toBe(500);
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('PATCH /api/apiKeys/:id/allowedEndpoints', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validObjectId = new mongoose.Types.ObjectId().toString();

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch(`/api/apiKeys/${validObjectId.toString()}/allowedEndpoints`);
                    expect(res.status).toBe(401);
                });

                it('should return 403 if the user does not have the required permissions', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);

                    const res = await request(app)
                        .patch(`/api/apiKeys/${validObjectId.toString()}/allowedEndpoints`)
                        .set('Authorization', nonAuthToken)
                        .send({ allowed_endpoints: [1, 2, 3] });

                    expect(res.status).toBe(403);
                });

                if (authMethod === 'user') {
                    it('should return 400 if the id is not a valid ObjectId', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                        const res = await request(app)
                            .patch('/api/apiKeys/invalid-id/allowedEndpoints')
                            .set('Authorization', authToken)
                            .send({ allowed_endpoints: [1, 2, 3] });

                        expect(res.status).toBe(400);
                    });

                    it('should return 400 if allowed_endpoints is not an array', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                        const res = await request(app)
                            .patch(`/api/apiKeys/${validObjectId.toString()}/allowedEndpoints`)
                            .set('Authorization', authToken)
                            .send({ allowed_endpoints: "not-an-array" });

                        expect(res.status).toBe(400);
                    });

                    it('should return 400 if allowed_endpoints contains non-integer values', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                        const res = await request(app)
                            .patch(`/api/apiKeys/${validObjectId.toString()}/allowedEndpoints`)
                            .set('Authorization', authToken)
                            .send({ allowed_endpoints: [1, "invalid-endpoint", 3] });

                        expect(res.status).toBe(400);
                    });

                    it('should return 404 if the API key does not exist', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        jest.spyOn(apiKeyService, 'updateAllowedEndpoints').mockResolvedValueOnce(null as any);

                        const res = await request(app)
                            .patch(`/api/apiKeys/${validObjectId.toString()}/allowedEndpoints`)
                            .set('Authorization', authToken)
                            .send({ allowed_endpoints: [1, 2, 3] });

                        expect(res.status).toBe(404);
                    });
                }

                it('should return 200 and update the API key with valid allowed_endpoints or 403 for api-key authentication', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    jest.spyOn(apiKeyService, 'updateAllowedEndpoints').mockResolvedValueOnce({ _id: validObjectId, allowed_endpoints: [1, 2, 3] } as any);

                    const res = await request(app)
                        .patch(`/api/apiKeys/${validObjectId}/allowedEndpoints`)
                        .set('Authorization', authToken)
                        .send({ allowed_endpoints: [1, 2, 3] });

                    if (authMethod === 'user') {
                        expect(res.status).toBe(200);
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });

                if (authMethod === 'user') {
                    it('should return 500 if an error occurs during the update process', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        jest.spyOn(apiKeyService, 'updateAllowedEndpoints').mockRejectedValueOnce(new Error('Database error'));

                        const res = await request(app)
                            .patch(`/api/apiKeys/${validObjectId}/allowedEndpoints`)
                            .set('Authorization', authToken)
                            .send({ allowed_endpoints: [1, 2, 3] });

                        expect(res.status).toBe(500);
                    });
                }
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('PATCH /api/apiKeys/:id/revoke', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validObjectId = '66c4a658077e3ad6e537d418'

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch(`/api/apiKeys/${validObjectId}/revoke`);
                    expect(res.status).toBe(401);
                });

                it('should return 403 if the user does not have the required permissions', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);

                    const res = await request(app)
                        .patch(`/api/apiKeys/${validObjectId}/revoke`)
                        .set('Authorization', nonAuthToken)
                        .send({ revoke: true });

                    expect(res.status).toBe(403);
                });

                if (authMethod === 'user') {
                    it('should return 400 if the id is not a valid ObjectId', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                        const res = await request(app)
                            .patch('/api/apiKeys/invalid-id/revoke')
                            .set('Authorization', authToken)
                            .send({ revoke: true });

                        expect(res.status).toBe(400);
                    });

                    it('should return 400 if revoke is not a boolean', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                        const res = await request(app)
                            .patch(`/api/apiKeys/${validObjectId}/revoke`)
                            .set('Authorization', authToken)
                            .send({ revoke: 'not-a-boolean' });

                        expect(res.status).toBe(400);
                    });

                    it('should return 404 if the API key does not exist', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        jest.spyOn(apiKeyService, 'updateRevocationStatus').mockResolvedValueOnce(null as any);

                        const res = await request(app)
                            .patch(`/api/apiKeys/${validObjectId}/revoke`)
                            .set('Authorization', authToken)
                            .send({ revoke: true });

                        expect(res.status).toBe(404);
                    });
                }

                it('should return 200 and revoke the API key if valid id and revoke status are provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    jest.spyOn(apiKeyService, 'updateRevocationStatus').mockResolvedValueOnce({ _id: validObjectId } as any);

                    const res = await request(app)
                        .patch(`/api/apiKeys/${validObjectId}/revoke`)
                        .set('Authorization', authToken)
                        .send({ revoke: true });

                    if (authMethod === 'user') {
                        expect(res.status).toBe(200);
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });

                if (authMethod === 'user') {
                    it('should return 500 if an error occurs during the revoke process', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        jest.spyOn(apiKeyService, 'updateRevocationStatus').mockRejectedValueOnce(new Error('Database error'));

                        const res = await request(app)
                            .patch(`/api/apiKeys/${validObjectId}/revoke`)
                            .set('Authorization', authToken)
                            .send({ revoke: true });

                        expect(res.status).toBe(500);
                    });
                }
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('DELETE /api/apiKeys/:id', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validObjectId = new mongoose.Types.ObjectId()

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).delete(`/api/apiKeys/${validObjectId.toString()}`);
                    expect(res.status).toBe(401);
                });

                it('should return 403 if the user does not have the required permissions', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);

                    const res = await request(app)
                        .delete(`/api/apiKeys/${validObjectId.toString()}`)
                        .set('Authorization', nonAuthToken);

                    expect(res.status).toBe(403);
                });

                if (authMethod === 'user') {
                    it('should return 400 if the id is not a valid ObjectId', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                        const res = await request(app)
                            .delete('/api/apiKeys/invalid-id')
                            .set('Authorization', authToken);

                        expect(res.status).toBe(400);
                    });

                    it('should return 404 if the API key does not exist', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        jest.spyOn(apiKeyService, 'delete').mockResolvedValueOnce(false as any);

                        const res = await request(app)
                            .delete(`/api/apiKeys/${validObjectId.toString()}`)
                            .set('Authorization', authToken);

                        expect(res.status).toBe(404);
                    });
                }

                it('should return 200 and delete the API key if a valid id is provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    jest.spyOn(apiKeyService, 'delete').mockResolvedValueOnce(true as any);

                    const res = await request(app)
                        .delete(`/api/apiKeys/${validObjectId.toString()}`)
                        .set('Authorization', authToken);

                    if (authMethod === 'user') {
                        expect(res.status).toBe(200);
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });

                if (authMethod === 'user') {
                    it('should return 500 if an error occurs during the delete process', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        jest.spyOn(apiKeyService, 'delete').mockRejectedValueOnce(new Error('Database error'));

                        const res = await request(app)
                            .delete(`/api/apiKeys/${validObjectId.toString()}`)
                            .set('Authorization', authToken);

                        expect(res.status).toBe(500);
                    });
                }
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('PATCH /api/apiKeys/:id/allowedVessels', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validObjectId = new mongoose.Types.ObjectId().toString();

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch(`/api/apiKeys/${validObjectId}/allowedVessels`);
                    expect(res.status).toBe(401);
                });

                it('should return 403 if the user does not have the required permissions', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);

                    const res = await request(app)
                        .patch(`/api/apiKeys/${validObjectId}/allowedVessels`)
                        .set('Authorization', nonAuthToken)
                        .send({ allowed_vessels: [new mongoose.Types.ObjectId().toString()] });

                    expect(res.status).toBe(403);
                });

                if (authMethod === 'user') {
                    it('should return 400 if the id is not a valid ObjectId', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                        const res = await request(app)
                            .patch('/api/apiKeys/invalid-id/allowedVessels')
                            .set('Authorization', authToken)
                            .send({ allowed_vessels: [new mongoose.Types.ObjectId().toString()] });

                        expect(res.status).toBe(400);
                    });

                    it('should return 400 if allowed_vessels is not an array', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                        const res = await request(app)
                            .patch(`/api/apiKeys/${validObjectId}/allowedVessels`)
                            .set('Authorization', authToken)
                            .send({ allowed_vessels: 'not-an-array' });

                        expect(res.status).toBe(400);
                    });

                    it('should return 400 if allowed_vessels contains invalid ObjectIds', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                        const res = await request(app)
                            .patch(`/api/apiKeys/${validObjectId}/allowedVessels`)
                            .set('Authorization', authToken)
                            .send({ allowed_vessels: ['invalid-object-id'] });

                        expect(res.status).toBe(400);
                    });

                    it('should return 404 if the API key does not exist', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        jest.spyOn(apiKeyService, 'updateAllowedVessels').mockResolvedValueOnce(null as any);

                        const res = await request(app)
                            .patch(`/api/apiKeys/${validObjectId}/allowedVessels`)
                            .set('Authorization', authToken)
                            .send({ allowed_vessels: [new mongoose.Types.ObjectId().toString()] });

                        expect(res.status).toBe(404);
                    });
                }

                it('should return 200 and update allowed_vessels or 403 for api-key authentication', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    jest.spyOn(apiKeyService, 'updateAllowedVessels').mockResolvedValueOnce({ _id: validObjectId, allowed_vessels: [new mongoose.Types.ObjectId().toString()] } as any);

                    const res = await request(app)
                        .patch(`/api/apiKeys/${validObjectId}/allowedVessels`)
                        .set('Authorization', authToken)
                        .send({ allowed_vessels: [new mongoose.Types.ObjectId().toString()] });

                    if (authMethod === 'user') {
                        expect(res.status).toBe(200);
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });

                if (authMethod === 'user') {
                    it('should return 500 if an error occurs during the update process', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        jest.spyOn(apiKeyService, 'updateAllowedVessels').mockRejectedValueOnce(new Error('Database error'));

                        const res = await request(app)
                            .patch(`/api/apiKeys/${validObjectId}/allowedVessels`)
                            .set('Authorization', authToken)
                            .send({ allowed_vessels: [new mongoose.Types.ObjectId().toString()] });

                        expect(res.status).toBe(500);
                    });
                }
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('PATCH /api/apiKeys/:id/details', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validObjectId = new mongoose.Types.ObjectId().toString();

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch(`/api/apiKeys/${validObjectId}/details`);
                    expect(res.status).toBe(401);
                });

                it('should return 403 if the user does not have the required permissions', async () => {
                    setupAuthMocks(authMethod, userOrApiKey, nonAuthToken, User, ApiKey);

                    const res = await request(app)
                        .patch(`/api/apiKeys/${validObjectId}/details`)
                        .set('Authorization', nonAuthToken)
                        .send({ description: 'x' });

                    expect(res.status).toBe(403);
                });

                if (authMethod === 'user') {
                    it('should return 400 if the id is not a valid ObjectId', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                        const res = await request(app)
                            .patch('/api/apiKeys/invalid-id/details')
                            .set('Authorization', authToken)
                            .send({ description: 'abc' });

                        expect(res.status).toBe(400);
                    });

                    it('should return 400 if description is missing or empty', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                        const res1 = await request(app)
                            .patch(`/api/apiKeys/${validObjectId}/details`)
                            .set('Authorization', authToken)
                            .send({});
                        expect(res1.status).toBe(400);

                        const res2 = await request(app)
                            .patch(`/api/apiKeys/${validObjectId}/details`)
                            .set('Authorization', authToken)
                            .send({ description: '' });
                        expect(res2.status).toBe(400);
                    });

                    it('should return 400 if email is invalid', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                        const res = await request(app)
                            .patch(`/api/apiKeys/${validObjectId}/details`)
                            .set('Authorization', authToken)
                            .send({ description: 'desc', email: 'not-an-email' });

                        expect(res.status).toBe(400);
                    });

                    it('should return 404 if the API key does not exist', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        jest.spyOn(apiKeyService, 'update').mockResolvedValueOnce(null as any);

                        const res = await request(app)
                            .patch(`/api/apiKeys/${validObjectId}/details`)
                            .set('Authorization', authToken)
                            .send({ description: 'desc', email: '<EMAIL>' });

                        expect(res.status).toBe(404);
                    });
                }

                it('should return 200 and update details or 403 for api-key authentication', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    jest.spyOn(apiKeyService, 'update').mockResolvedValueOnce({ _id: validObjectId, description: 'desc' } as any);

                    const res = await request(app)
                        .patch(`/api/apiKeys/${validObjectId}/details`)
                        .set('Authorization', authToken)
                        .send({ description: 'desc', email: '<EMAIL>' });

                    if (authMethod === 'user') {
                        expect(res.status).toBe(200);
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });

                if (authMethod === 'user') {
                    it('should return 500 if an error occurs during the update process', async () => {
                        setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                        jest.spyOn(apiKeyService, 'update').mockRejectedValueOnce(new Error('Database error'));

                        const res = await request(app)
                            .patch(`/api/apiKeys/${validObjectId}/details`)
                            .set('Authorization', authToken)
                            .send({ description: 'desc', email: '<EMAIL>' });

                        expect(res.status).toBe(500);
                    });
                }
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});
