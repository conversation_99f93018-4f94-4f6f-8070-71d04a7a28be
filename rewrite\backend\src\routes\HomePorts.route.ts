import express, { Request, Response } from "express";
import { validateError } from "../utils/functions";
import isAuthenticated from "../middlewares/auth";
import assignEndpointId from "../middlewares/assignEndpointId";
import { endpointIds } from "../utils/endpointIds";
import HomePort from "../models/HomePort";
import compression from "compression";
import rateLimit from "express-rate-limit";
import { IHomePort } from "../interfaces/HomePort";

const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 15,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);
router.use(compression());

router.get("/", assignEndpointId.bind(this, endpointIds.FETCH_ALL_HOME_PORTS), isAuthenticated, async (_req: Request, res: Response) => {
    try {
        const homePorts: IHomePort[] = await HomePort.aggregate([
            {
                $project: {
                    _id: 1,
                    lat: 1,
                    lng: 1,
                },
            },
        ]);
        res.status(200).json(homePorts);
    } catch (err) {
        validateError(err, res);
    }
});

export default router;
